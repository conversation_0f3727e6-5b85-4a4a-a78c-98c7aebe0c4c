<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mesure extends Model
{
    use HasFactory;

    protected $fillable = [
        'created_by',
        'equipe_id',
        'region_id',
        'g_rx_power',
        'g_tx_power',
        'g_voltage',
        'g_bias_current',
        'g_pon_alarm_info',
        'xdsl_mode',
        'xdsl_channel_mode',
        'xdsl_up_stream',
        'xdsl_down_stream',
        'xdsl_max_up_attainable_rate',
        'xdsl_max_down_attainable_rate',
        'xdsl_attenuation_up_rate',
        'xdsl_attenuation_down_rate',
        'xdsl_snr_margin_up_stream',
        'xdsl_snr_margin_down_stream',
        'xdsl_crc_errors',
        'mesure_type',
        'mesure_status',
        'num_ligne',
        'emplacement',
        'lat',
        'lng',
        'mesure_date'
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function equipe()
    {
        return $this->belongsTo(Equipe::class);
    }

    public function region()
    {
        return $this->belongsTo(Region::class);
    }
}