<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CleanupDuplicatePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup duplicate permissions for Mesure model';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of duplicate permissions...');

        // Get all permissions related to Mesure
        $mesurePermissions = Permission::where('name', 'like', '%mesure%')->get();
        
        // Group permissions by their base name (without the prefix)
        $groupedPermissions = [];
        foreach ($mesurePermissions as $permission) {
            $baseName = preg_replace('/^(view|view_any|create|update|delete|delete_any|restore|restore_any|replicate|reorder|force_delete|force_delete_any)_/', '', $permission->name);
            
            if (!isset($groupedPermissions[$baseName])) {
                $groupedPermissions[$baseName] = [];
            }
            
            $groupedPermissions[$baseName][] = $permission;
        }
        
        // For each group, keep only one permission and delete the others
        foreach ($groupedPermissions as $baseName => $permissions) {
            if (count($permissions) > 1) {
                $this->info("Found {$baseName} with " . count($permissions) . " duplicate permissions");
                
                // Keep the first permission
                $keepPermission = $permissions[0];
                
                // For each other permission, transfer its roles to the kept permission
                for ($i = 1; $i < count($permissions); $i++) {
                    $deletePermission = $permissions[$i];
                    
                    // Get all roles that have this permission
                    $roles = Role::whereHas('permissions', function ($query) use ($deletePermission) {
                        $query->where('id', $deletePermission->id);
                    })->get();
                    
                    // Assign the kept permission to these roles
                    foreach ($roles as $role) {
                        if (!$role->hasPermissionTo($keepPermission)) {
                            $role->givePermissionTo($keepPermission);
                            $this->info("Assigned {$keepPermission->name} to role {$role->name}");
                        }
                    }
                    
                    // Delete the duplicate permission
                    $this->info("Deleting duplicate permission: {$deletePermission->name}");
                    $deletePermission->delete();
                }
            }
        }
        
        $this->info('Cleanup completed successfully!');
        
        return Command::SUCCESS;
    }
}
