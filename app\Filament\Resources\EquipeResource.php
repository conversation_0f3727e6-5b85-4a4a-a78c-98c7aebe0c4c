<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EquipeResource\Pages;
use App\Models\Equipe;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EquipeResource extends Resource
{
    protected static ?string $model = Equipe::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Équipes';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 2; // Pour s'assurer que le groupe apparaît après Service GPON

    // Let Shield handle the permissions
    // No need to override shouldRegisterNavigation

    protected static ?string $modelLabel = 'Équipe';

    protected static ?string $pluralModelLabel = 'Équipes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nom')
                    ->required()
                    ->maxLength(255)
                    ->label('Nom de l\'équipe'),
                Forms\Components\Select::make('regions')
                    ->relationship('regions', 'nom')
                    ->multiple()
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Régions'),
                Forms\Components\Select::make('responsables')
                    ->relationship('responsables', 'name')
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->label('Responsables'),
                Forms\Components\Select::make('agents')
                    ->relationship('agents', 'name')
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->label('Agents'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nom')
                    ->searchable()
                    ->sortable()
                    ->label('Nom'),
                Tables\Columns\TextColumn::make('regions.nom')
                    ->listWithLineBreaks()
                    ->limitList(3)
                    ->searchable()
                    ->sortable()
                    ->label('Régions'),
                Tables\Columns\TextColumn::make('responsables.name')
                    ->listWithLineBreaks()
                    ->limitList(3)
                    ->label('Responsables'),
                Tables\Columns\TextColumn::make('agents.name')
                    ->listWithLineBreaks()
                    ->limitList(3)
                    ->label('Agents'),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Créé par')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Créé le'),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Supprimé le'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEquipes::route('/'),
            'create' => Pages\CreateEquipe::route('/create'),
            'edit' => Pages\EditEquipe::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}