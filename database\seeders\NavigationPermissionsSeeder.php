<?php

namespace Database\Seeders;

use App\Support\NavigationPermissions;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class NavigationPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create navigation permissions
        foreach (NavigationPermissions::all() as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Get the super_admin role
        $superAdminRole = Role::where('name', 'super_admin')->first();
        if ($superAdminRole) {
            // Give all navigation permissions to super_admin
            $navigationPermissions = Permission::whereIn('name', array_keys(NavigationPermissions::all()))->get();
            $superAdminRole->givePermissionTo($navigationPermissions);
        }

        // Don't automatically assign permissions to other roles
        // Let the admin manage these permissions through the UI

        $this->command->info('Navigation permissions created and assigned to super_admin role.');
    }
}
