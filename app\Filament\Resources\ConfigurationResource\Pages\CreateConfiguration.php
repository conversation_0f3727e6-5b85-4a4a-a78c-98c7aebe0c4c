<?php

namespace App\Filament\Resources\ConfigurationResource\Pages;

use App\Filament\Resources\ConfigurationResource;
use App\Models\Configuration;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateConfiguration extends CreateRecord
{
    protected static string $resource = ConfigurationResource::class;

    protected static bool $canCreateAnother = false;

    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Check if a configuration with the same type already exists
        $existingConfig = Configuration::where('type', $data['type'])->first();

        if ($existingConfig) {
            // Update the existing record instead of creating a new one
            $existingConfig->update($data);
            return $existingConfig;
        }

        // If no existing record, create a new one
        return static::getModel()::create($data);
    }
}
