<?php

namespace {{ namespace }};

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class {{ managerClass }} extends RelationManager
{
    protected static string $relationship = '{{ relationship }}';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('{{ recordTitleAttribute }}')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('{{ recordTitleAttribute }}')
            ->columns([
                Tables\Columns\TextColumn::make('{{ recordTitleAttribute }}'),
            ])
            ->filters([
{{ tableFilters }}
            ])
            ->headerActions([
{{ tableHeaderActions }}
            ])
            ->actions([
{{ tableActions }}
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
{{ tableBulkActions }}
                ]),
            ]){{ modifyQueryUsing }};
    }
}
