<?php

namespace App\Filament\Resources\XDSLServiceResource\Pages;

use App\Filament\Resources\XDSLServiceResource;
use App\Models\Mesure;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ListXDSLServices extends ListRecords
{
    protected static string $resource = XDSLServiceResource::class;

    protected function getTableFiltersFormWidth(): string
    {
        return '4xl';
    }

    protected function getTableFiltersFormMaxHeight(): string
    {
        return '600px';
    }

    protected function hasSearchContainer(): bool
    {
        return false;
    }

    protected function hasFiltersDropdown(): bool
    {
        return false;
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    public function getHeader(): ?View
    {
        // Récupérer les filtres de date appliqués
        $startDate = $this->tableFilters['date_range']['created_from'] ?? null;
        $endDate = $this->tableFilters['date_range']['created_until'] ?? null;

        // Récupérer l'utilisateur connecté
        $user = Auth::user();

        // Récupérer le rôle de l'utilisateur
        $userRole = DB::table('roles')
            ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('model_has_roles.model_type', get_class($user))
            ->select('roles.id')
            ->first();

        // Créer la requête de base
        $query = Mesure::query()->where('mesure_type', 'xdsl');

        // Appliquer les filtres de date
        if ($startDate) {
            $query->whereDate('mesure_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('mesure_date', '<=', $endDate);
        }

        // Appliquer les filtres basés sur le rôle - utiliser la même logique que dans GPONServiceResource::table()
        // Si l'utilisateur est super_admin (id=1) ou admin (id=2), afficher toutes les mesures
        if ($userRole && ($userRole->id == 1 || $userRole->id == 2)) {
            // Ne pas ajouter de condition supplémentaire
        }
        // Si l'utilisateur est chef d'équipe (id=3), filtrer par ses équipes
        elseif ($userRole && $userRole->id == 3) {
            // Récupérer les équipes dont l'utilisateur est responsable
            $equipeIds = DB::table('equipe_responsable')
                ->where('user_id', $user->id)
                ->pluck('equipe_id')
                ->toArray();

            if (!empty($equipeIds)) {
                $query->whereIn('equipe_id', $equipeIds);
            } else {
                // Si l'utilisateur n'a pas d'équipes, ne rien afficher
                $query->whereRaw('1 = 0');
            }
        }
        // Pour les autres rôles (y compris id=4), ne montrer que les mesures créées par l'utilisateur
        else {
            $query->where('created_by', $user->id);
        }

        // Compter le nombre total de mesures
        $totalMesures = $query->count();

        // Compter le nombre d'abonnés distincts
        $totalAbonnes = (clone $query)
            ->whereNotNull('num_ligne')
            ->distinct('num_ligne')
            ->count('num_ligne');

        return view('filament.resources.xdsl-service-resource.pages.filters-header', [
            'totalMesures' => $totalMesures,
            'totalAbonnes' => $totalAbonnes,
        ]);
    }

    protected function hasBreadcrumbs(): bool
    {
        return false;
    }

    public function filter(): void
    {
        // Manually apply filters when the Filter button is clicked
        $this->resetPage();

        // Force a table refresh
        $this->dispatch('refresh');
    }

    public function resetTableFiltersForm(): void
    {
        // Réinitialiser tous les filtres
        $this->tableFilters = [];

        // Réinitialiser la pagination
        $this->resetPage();

        // Forcer le rafraîchissement de la page
        $this->dispatch('refresh');
    }
}
