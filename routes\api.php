<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RegionController;
use App\Http\Controllers\Api\UserLocationController;
use App\Http\Controllers\MesureController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


// Route::post('/login', [AuthController::class, 'login']);

// Mesure routes
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('mesures', MesureController::class);
});

// Routes pour les filtres du dashboard
Route::get('/regions/{region}/equipes', [RegionController::class, 'getEquipes']);
Route::get('/equipes', [RegionController::class, 'getAllEquipes']);

// Route pour récupérer les localisations des utilisateurs
Route::get('/users/{userId}/locations', [UserLocationController::class, 'getUserLocations']);
