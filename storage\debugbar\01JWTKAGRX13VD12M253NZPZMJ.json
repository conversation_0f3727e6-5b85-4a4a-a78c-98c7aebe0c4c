{"__meta": {"id": "01JWTKAGRX13VD12M253NZPZMJ", "datetime": "2025-06-03 09:52:52", "utime": **********.510505, "method": "GET", "uri": "/?region_id=&equipe_id=9&date=2025-05-26", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748944370.955079, "end": **********.51052, "duration": 1.555440902709961, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1748944370.955079, "relative_start": 0, "end": **********.481573, "relative_end": **********.481573, "duration": 0.***************, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.481586, "relative_start": 0.****************, "end": **********.510521, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.640289, "relative_start": 0.****************, "end": **********.64213, "relative_end": **********.64213, "duration": 0.001840829849243164, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::pages.dashboard", "start": **********.950255, "relative_start": 0.***************, "end": **********.950255, "relative_end": **********.950255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.dashboardadmin", "start": **********.970148, "relative_start": 1.****************, "end": **********.970148, "relative_end": **********.970148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.232773, "relative_start": 1.****************, "end": **********.232773, "relative_end": **********.232773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.235468, "relative_start": 1.2803888320922852, "end": **********.235468, "relative_end": **********.235468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.238952, "relative_start": 1.2838728427886963, "end": **********.238952, "relative_end": **********.238952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.240815, "relative_start": 1.285735845565796, "end": **********.240815, "relative_end": **********.240815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.241936, "relative_start": 1.2868568897247314, "end": **********.241936, "relative_end": **********.241936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.244269, "relative_start": 1.2891898155212402, "end": **********.244269, "relative_end": **********.244269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.249248, "relative_start": 1.2941689491271973, "end": **********.249248, "relative_end": **********.249248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.249971, "relative_start": 1.2948918342590332, "end": **********.249971, "relative_end": **********.249971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.385375, "relative_start": 1.4302959442138672, "end": **********.385375, "relative_end": **********.385375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.386538, "relative_start": 1.4314589500427246, "end": **********.386538, "relative_end": **********.386538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.387788, "relative_start": 1.432708978652954, "end": **********.387788, "relative_end": **********.387788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.389932, "relative_start": 1.4348528385162354, "end": **********.389932, "relative_end": **********.389932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.391286, "relative_start": 1.4362068176269531, "end": **********.391286, "relative_end": **********.391286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.39251, "relative_start": 1.4374308586120605, "end": **********.39251, "relative_end": **********.39251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.393514, "relative_start": 1.4384348392486572, "end": **********.393514, "relative_end": **********.393514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.39427, "relative_start": 1.4391908645629883, "end": **********.39427, "relative_end": **********.39427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.397732, "relative_start": 1.442652940750122, "end": **********.397732, "relative_end": **********.397732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.398248, "relative_start": 1.4431688785552979, "end": **********.398248, "relative_end": **********.398248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.398772, "relative_start": 1.443692922592163, "end": **********.398772, "relative_end": **********.398772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.39929, "relative_start": 1.4442110061645508, "end": **********.39929, "relative_end": **********.39929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.399632, "relative_start": 1.4445528984069824, "end": **********.399632, "relative_end": **********.399632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.399977, "relative_start": 1.4448978900909424, "end": **********.399977, "relative_end": **********.399977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.400362, "relative_start": 1.4452829360961914, "end": **********.400362, "relative_end": **********.400362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.400681, "relative_start": 1.4456019401550293, "end": **********.400681, "relative_end": **********.400681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.401063, "relative_start": 1.44598388671875, "end": **********.401063, "relative_end": **********.401063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.401326, "relative_start": 1.446246862411499, "end": **********.401326, "relative_end": **********.401326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.401598, "relative_start": 1.446518898010254, "end": **********.401598, "relative_end": **********.401598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.402064, "relative_start": 1.4469850063323975, "end": **********.402064, "relative_end": **********.402064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.40434, "relative_start": 1.449260950088501, "end": **********.40434, "relative_end": **********.40434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.405254, "relative_start": 1.4501748085021973, "end": **********.405254, "relative_end": **********.405254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.406268, "relative_start": 1.4511888027191162, "end": **********.406268, "relative_end": **********.406268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.406814, "relative_start": 1.451735019683838, "end": **********.406814, "relative_end": **********.406814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.407472, "relative_start": 1.452392816543579, "end": **********.407472, "relative_end": **********.407472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.407805, "relative_start": 1.452725887298584, "end": **********.407805, "relative_end": **********.407805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.407981, "relative_start": 1.452901840209961, "end": **********.407981, "relative_end": **********.407981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.408423, "relative_start": 1.4533438682556152, "end": **********.408423, "relative_end": **********.408423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar-logo", "start": **********.44094, "relative_start": 1.485860824584961, "end": **********.44094, "relative_end": **********.44094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.441602, "relative_start": 1.486522912979126, "end": **********.441602, "relative_end": **********.441602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.442521, "relative_start": 1.4874420166015625, "end": **********.442521, "relative_end": **********.442521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.443015, "relative_start": 1.487936019897461, "end": **********.443015, "relative_end": **********.443015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.443965, "relative_start": 1.4888858795166016, "end": **********.443965, "relative_end": **********.443965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.445767, "relative_start": 1.4906878471374512, "end": **********.445767, "relative_end": **********.445767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.4468, "relative_start": 1.4917209148406982, "end": **********.4468, "relative_end": **********.4468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.447415, "relative_start": 1.4923360347747803, "end": **********.447415, "relative_end": **********.447415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.447877, "relative_start": 1.4927978515625, "end": **********.447877, "relative_end": **********.447877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.448359, "relative_start": 1.4932799339294434, "end": **********.448359, "relative_end": **********.448359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.448845, "relative_start": 1.4937658309936523, "end": **********.448845, "relative_end": **********.448845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.449522, "relative_start": 1.4944429397583008, "end": **********.449522, "relative_end": **********.449522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.450297, "relative_start": 1.49521803855896, "end": **********.450297, "relative_end": **********.450297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.450876, "relative_start": 1.4957969188690186, "end": **********.450876, "relative_end": **********.450876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.452104, "relative_start": 1.4970250129699707, "end": **********.452104, "relative_end": **********.452104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.452973, "relative_start": 1.4978938102722168, "end": **********.452973, "relative_end": **********.452973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.453653, "relative_start": 1.4985740184783936, "end": **********.453653, "relative_end": **********.453653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.454346, "relative_start": 1.4992668628692627, "end": **********.454346, "relative_end": **********.454346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.455122, "relative_start": 1.5000429153442383, "end": **********.455122, "relative_end": **********.455122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.455844, "relative_start": 1.5007648468017578, "end": **********.455844, "relative_end": **********.455844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.456158, "relative_start": 1.5010788440704346, "end": **********.456158, "relative_end": **********.456158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.45667, "relative_start": 1.5015909671783447, "end": **********.45667, "relative_end": **********.45667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.456961, "relative_start": 1.5018818378448486, "end": **********.456961, "relative_end": **********.456961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.457427, "relative_start": 1.5023479461669922, "end": **********.457427, "relative_end": **********.457427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.458502, "relative_start": 1.5034229755401611, "end": **********.458502, "relative_end": **********.458502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.45978, "relative_start": 1.5047008991241455, "end": **********.45978, "relative_end": **********.45978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.460798, "relative_start": 1.5057189464569092, "end": **********.460798, "relative_end": **********.460798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.461174, "relative_start": 1.5060949325561523, "end": **********.461174, "relative_end": **********.461174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.461639, "relative_start": 1.5065598487854004, "end": **********.461639, "relative_end": **********.461639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.462029, "relative_start": 1.5069499015808105, "end": **********.462029, "relative_end": **********.462029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.462452, "relative_start": 1.5073728561401367, "end": **********.462452, "relative_end": **********.462452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.462756, "relative_start": 1.5076768398284912, "end": **********.462756, "relative_end": **********.462756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.463199, "relative_start": 1.508119821548462, "end": **********.463199, "relative_end": **********.463199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.463551, "relative_start": 1.508471965789795, "end": **********.463551, "relative_end": **********.463551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.463937, "relative_start": 1.5088579654693604, "end": **********.463937, "relative_end": **********.463937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.464232, "relative_start": 1.509152889251709, "end": **********.464232, "relative_end": **********.464232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.464936, "relative_start": 1.509856939315796, "end": **********.464936, "relative_end": **********.464936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.466126, "relative_start": 1.5110468864440918, "end": **********.466126, "relative_end": **********.466126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.466824, "relative_start": 1.5117449760437012, "end": **********.466824, "relative_end": **********.466824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.4673, "relative_start": 1.512220859527588, "end": **********.4673, "relative_end": **********.4673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.467856, "relative_start": 1.5127768516540527, "end": **********.467856, "relative_end": **********.467856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": **********.468285, "relative_start": 1.5132060050964355, "end": **********.468285, "relative_end": **********.468285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.469189, "relative_start": 1.5141098499298096, "end": **********.469189, "relative_end": **********.469189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.469663, "relative_start": 1.5145838260650635, "end": **********.469663, "relative_end": **********.469663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.470053, "relative_start": 1.5149738788604736, "end": **********.470053, "relative_end": **********.470053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.470666, "relative_start": 1.5155868530273438, "end": **********.470666, "relative_end": **********.470666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.471445, "relative_start": 1.5163660049438477, "end": **********.471445, "relative_end": **********.471445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.47229, "relative_start": 1.5172109603881836, "end": **********.47229, "relative_end": **********.47229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.473644, "relative_start": 1.5185649394989014, "end": **********.473644, "relative_end": **********.473644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.474276, "relative_start": 1.5191969871520996, "end": **********.474276, "relative_end": **********.474276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.475285, "relative_start": 1.5202059745788574, "end": **********.475285, "relative_end": **********.475285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.476198, "relative_start": 1.5211188793182373, "end": **********.476198, "relative_end": **********.476198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.476415, "relative_start": 1.5213358402252197, "end": **********.476415, "relative_end": **********.476415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.481074, "relative_start": 1.5259950160980225, "end": **********.481074, "relative_end": **********.481074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.482058, "relative_start": 1.5269789695739746, "end": **********.482058, "relative_end": **********.482058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.484768, "relative_start": 1.529688835144043, "end": **********.484874, "relative_end": **********.484874, "duration": 0.00010609626770019531, "duration_str": "106μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.506398, "relative_start": 1.551318883895874, "end": **********.506495, "relative_end": **********.506495, "duration": 9.703636169433594e-05, "duration_str": "97μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 46883048, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 93, "nb_templates": 93, "templates": [{"name": "1x filament-panels::pages.dashboard", "param_count": null, "params": [], "start": **********.950241, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/pages/dashboard.blade.phpfilament-panels::pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::pages.dashboard"}, {"name": "1x partials.dashboardadmin", "param_count": null, "params": [], "start": **********.970137, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.phppartials.dashboardadmin", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=1", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.dashboard<PERSON>min"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.23275, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.235436, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "3x filament::components.modal.index", "param_count": null, "params": [], "start": **********.238936, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.244231, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.249228, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.249928, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.385365, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "8x filament::components.icon-button", "param_count": null, "params": [], "start": **********.386529, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 8, "name_original": "filament::components.icon-button"}, {"name": "30x filament::components.icon", "param_count": null, "params": [], "start": **********.387779, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 30, "name_original": "filament::components.icon"}, {"name": "1x __components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.392501, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e93d402ed1f3da8a07f4840136a03cb"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.393506, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.394262, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.397718, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.398237, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.399283, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.399624, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.40159, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "3x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.402053, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.407973, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.index"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.408415, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.sidebar-logo", "param_count": null, "params": [], "start": **********.440929, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar-logo.blade.phpfilament-panels::components.sidebar-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar-logo.blade.php&line=1", "ajax": false, "filename": "sidebar-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar-logo"}, {"name": "5x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.445756, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament-panels::components.sidebar.group"}, {"name": "15x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.446788, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 15, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": **********.468277, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.474264, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.475275, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.47619, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.476407, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.482026, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}]}, "queries": {"count": 31, "nb_statements": 31, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.6450400000000001, "accumulated_duration_str": "645ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC'", "type": "query", "params": [], "bindings": ["KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.647601, "duration": 0.07623, "duration_str": "76.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 11.818}, {"sql": "select top 1 * from [users] where [id] = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.731574, "duration": 0.01723, "duration_str": "17.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 11.818, "width_percent": 2.671}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7537, "duration": 0.01855, "duration_str": "18.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 14.489, "width_percent": 2.876}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.777712, "duration": 0.01771, "duration_str": "17.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 17.365, "width_percent": 2.746}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.79681, "duration": 0.01759, "duration_str": "17.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 20.11, "width_percent": 2.727}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 11 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [11, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.8157248, "duration": 0.01688, "duration_str": "16.88ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 22.837, "width_percent": 2.617}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.839375, "duration": 0.01785, "duration_str": "17.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 25.454, "width_percent": 2.767}, {"sql": "select * from [cache] where [key] in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.858198, "duration": 0.02534, "duration_str": "25.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 28.222, "width_percent": 3.928}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.884297, "duration": 0.020739999999999998, "duration_str": "20.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 32.15, "width_percent": 3.215}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.905834, "duration": 0.01726, "duration_str": "17.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 35.365, "width_percent": 2.676}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.924943, "duration": 0.01744, "duration_str": "17.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 38.041, "width_percent": 2.704}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 11 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [11, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "filament-panels::pages.dashboard", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/pages/dashboard.blade.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9526849, "duration": 0.01691, "duration_str": "16.91ms", "memory": 0, "memory_str": null, "filename": "filament-panels::pages.dashboard:30", "source": {"index": 14, "namespace": "view", "name": "filament-panels::pages.dashboard", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/pages/dashboard.blade.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fpages%2Fdashboard.blade.php&line=30", "ajax": false, "filename": "dashboard.blade.php", "line": "30"}, "connection": "nourtel", "explain": null, "start_percent": 40.745, "width_percent": 2.622}, {"sql": "select * from [regions] where [regions].[deleted_at] is null order by [nom] asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.973233, "duration": 0.01722, "duration_str": "17.22ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:15", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=15", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "15"}, "connection": "nourtel", "explain": null, "start_percent": 43.366, "width_percent": 2.67}, {"sql": "select * from [equipes] where [equipes].[deleted_at] is null order by [nom] asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.992214, "duration": 0.01779, "duration_str": "17.79ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:23", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=23", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "23"}, "connection": "nourtel", "explain": null, "start_percent": 46.036, "width_percent": 2.758}, {"sql": "select top 1 COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] = '9' and cast([mesure_date] as date) = '2025-05-26'", "type": "query", "params": [], "bindings": ["xdsl", "9", "2025-05-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.012069, "duration": 0.01855, "duration_str": "18.55ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:44", "source": {"index": 17, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=44", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "44"}, "connection": "nourtel", "explain": null, "start_percent": 48.794, "width_percent": 2.876}, {"sql": "select CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] = '9' and month([mesure_date]) = '05' group by CONVERT(DATE, mesure_date)", "type": "query", "params": [], "bindings": ["xdsl", "9", "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0317729, "duration": 0.01816, "duration_str": "18.16ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:48", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=48", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "48"}, "connection": "nourtel", "explain": null, "start_percent": 51.67, "width_percent": 2.815}, {"sql": "select top 1 CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] = '9' and month([mesure_date]) = '05' group by CONVERT(DATE, mesure_date) order by [total] desc", "type": "query", "params": [], "bindings": ["xdsl", "9", "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.051451, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:54", "source": {"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=54", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 54.485, "width_percent": 2.677}, {"sql": "select top 1 COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'gpon' and [equipe_id] = '9' and cast([mesure_date] as date) = '2025-05-26'", "type": "query", "params": [], "bindings": ["gpon", "9", "2025-05-26"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0712361, "duration": 0.01805, "duration_str": "18.05ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:59", "source": {"index": 17, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=59", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 57.162, "width_percent": 2.798}, {"sql": "select CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'gpon' and [equipe_id] = '9' and month([mesure_date]) = '05' group by CONVERT(DATE, mesure_date)", "type": "query", "params": [], "bindings": ["gpon", "9", "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.091536, "duration": 0.01712, "duration_str": "17.12ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:63", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=63", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "63"}, "connection": "nourtel", "explain": null, "start_percent": 59.961, "width_percent": 2.654}, {"sql": "select top 1 CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total from [mesures] where [mesure_type] = 'gpon' and [equipe_id] = '9' and month([mesure_date]) = '05' group by CONVERT(DATE, mesure_date) order by [total] desc", "type": "query", "params": [], "bindings": ["gpon", "9", "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 69}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.1097271, "duration": 0.01859, "duration_str": "18.59ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:69", "source": {"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=69", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "69"}, "connection": "nourtel", "explain": null, "start_percent": 62.615, "width_percent": 2.882}, {"sql": "select [created_by], created_by,\nCOUNT(DISTINCT CASE WHEN mesure_type = 'xdsl' THEN CONCAT(CONVERT(DATE, mesure_date), '_', num_ligne) END) as xdsl_count,\nCOUNT(DISTINCT CASE WHEN mesure_type = 'gpon' THEN CONCAT(CONVERT(DATE, mesure_date), '_', num_ligne) END) as gpon_count from [mesures] where [created_by] is not null and month([mesure_date]) = '05' and [equipe_id] = '9' group by [created_by] having COUNT(DISTINCT CASE WHEN mesure_type = 'xdsl' THEN CONCAT(CONVERT(DATE, mesure_date), '_', num_ligne) END) + COUNT(DISTINCT CASE WHEN mesure_type = 'gpon' THEN CONCAT(CONVERT(DATE, mesure_date), '_', num_ligne) END) > 0", "type": "query", "params": [], "bindings": ["05", "9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 91}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.129704, "duration": 0.01848, "duration_str": "18.48ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:91", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=91", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "91"}, "connection": "nourtel", "explain": null, "start_percent": 65.497, "width_percent": 2.865}, {"sql": "select * from [users] where [id] in ('27', '28')", "type": "query", "params": [], "bindings": ["27", "28"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.149384, "duration": 0.01866, "duration_str": "18.66ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:95", "source": {"index": 15, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=95", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "95"}, "connection": "nourtel", "explain": null, "start_percent": 68.362, "width_percent": 2.893}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (27, 28) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 95}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.1697738, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:95", "source": {"index": 20, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=95", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "95"}, "connection": "nourtel", "explain": null, "start_percent": 71.254, "width_percent": 2.733}, {"sql": "select top 1 1 [exists] from [mesures] where [created_by] = '27' and [lat] is not null and [lng] is not null", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 108}, {"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 98}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.19045, "duration": 0.01759, "duration_str": "17.59ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:108", "source": {"index": 11, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=108", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "108"}, "connection": "nourtel", "explain": null, "start_percent": 73.988, "width_percent": 2.727}, {"sql": "select top 1 1 [exists] from [mesures] where [created_by] = '28' and [lat] is not null and [lng] is not null", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 108}, {"index": 16, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 98}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.211454, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "partials.dashboardadmin:108", "source": {"index": 11, "namespace": "view", "name": "partials.dashboard<PERSON>min", "file": "D:\\Projects\\nourtel\\resources\\views/partials/dashboardadmin.blade.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fpartials%2Fdashboardadmin.blade.php&line=108", "ajax": false, "filename": "dashboardadmin.blade.php", "line": "108"}, "connection": "nourtel", "explain": null, "start_percent": 76.715, "width_percent": 2.922}, {"sql": "select * from [cache] where [key] in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.253061, "duration": 0.04218, "duration_str": "42.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 79.637, "width_percent": 6.539}, {"sql": "select [permissions].*, [model_has_permissions].[model_id] as [pivot_model_id], [model_has_permissions].[permission_id] as [pivot_permission_id], [model_has_permissions].[model_type] as [pivot_model_type] from [permissions] inner join [model_has_permissions] on [permissions].[id] = [model_has_permissions].[permission_id] where [model_has_permissions].[model_id] in (11) and [model_has_permissions].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.303394, "duration": 0.01855, "duration_str": "18.55ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "nourtel", "explain": null, "start_percent": 86.176, "width_percent": 2.876}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (11) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.323622, "duration": 0.01834, "duration_str": "18.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "nourtel", "explain": null, "start_percent": 89.052, "width_percent": 2.843}, {"sql": "select count(*) as aggregate from [roles]", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\RoleResource.php", "line": 194}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.362613, "duration": 0.016829999999999998, "duration_str": "16.83ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:194", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\RoleResource.php", "line": 194}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=194", "ajax": false, "filename": "RoleResource.php", "line": "194"}, "connection": "nourtel", "explain": null, "start_percent": 91.895, "width_percent": 2.609}, {"sql": "select count(*) as aggregate from [roles]", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\RoleResource.php", "line": 194}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.419289, "duration": 0.016980000000000002, "duration_str": "16.98ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:194", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\RoleResource.php", "line": 194}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=194", "ajax": false, "filename": "RoleResource.php", "line": "194"}, "connection": "nourtel", "explain": null, "start_percent": 94.504, "width_percent": 2.632}, {"sql": "update [sessions] set [payload] = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUWtobGhvYWV6MnFXV1piaGNHRGxUeEtFSW5hQmFNUkwwZEpHak5yRCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjYwOiJodHRwczovL25vdXJ0ZWwudGVzdC8/ZGF0ZT0yMDI1LTA1LTI2JmVxdWlwZV9pZD05JnJlZ2lvbl9pZD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxMTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEltdjZFNkp6L29EUFE3Q28zMEpKRS5zM21DZmpJZmNKZkZLaXF1NW83M3k2azFoa3RxSmttIjt9', [last_activity] = **********, [user_id] = 11, [ip_address] = '127.0.0.1', [user_agent] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where [id] = 'KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUWtobGhvYWV6MnFXV1piaGNHRGxUeEtFSW5hQmFNUkwwZEpHak5yRCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjYwOiJodHRwczovL25vdXJ0ZWwudGVzdC8/ZGF0ZT0yMDI1LTA1LTI2JmVxdWlwZV9pZD05JnJlZ2lvbl9pZD0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxMTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEltdjZFNkp6L29EUFE3Q28zMEpKRS5zM21DZmpJZmNKZkZLaXF1NW83M3k2azFoa3RxSmttIjt9", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.487305, "duration": 0.01847, "duration_str": "18.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "nourtel", "explain": null, "start_percent": 97.137, "width_percent": 2.863}]}, "models": {"data": {"App\\Models\\Region": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FRegion.php&line=1", "ajax": false, "filename": "Region.php", "line": "?"}}, "App\\Models\\Equipe": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FEquipe.php&line=1", "ajax": false, "filename": "Equipe.php", "line": "?"}}, "App\\Models\\Mesure": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 46, "is_counter": true}, "livewire": {"data": {"filament.pages.dashboard #pPtveBklM5lapUCL51gP": "array:4 [\n  \"data\" => array:14 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"filament.pages.dashboard\"\n  \"component\" => \"Filament\\Pages\\Dashboard\"\n  \"id\" => \"pPtveBklM5lapUCL51gP\"\n]", "filament.livewire.notifications #kI5SVrznhbqvCEtdRH5J": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2690\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"kI5SVrznhbqvCEtdRH5J\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 36, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-881292873 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881292873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.3455, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309552050 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309552050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.347796, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1667933304 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667933304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351252, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2138903396 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138903396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351399, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-189835886 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189835886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353175, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1863288663 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863288663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353351, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2055854906 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055854906\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356279, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-360827322 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360827322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356392, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-805759476 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805759476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35771, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1039414326 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039414326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.357812, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-86056901 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86056901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.359261, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1006214990 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006214990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.359351, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1235354360 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235354360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.360186, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 11,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-115586908 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115586908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.360328, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-407208714 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407208714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381083, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-982247964 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982247964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381211, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2095810790 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095810790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384616, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 11,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-224783955 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224783955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384732, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1917760000 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917760000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.411009, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-524161150 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524161150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.41177, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1546670783 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546670783\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413341, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-543460890 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543460890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413432, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1028820907 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028820907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414266, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-724140913 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724140913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414351, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2084711368 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084711368\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415003, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-76865847 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76865847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415086, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-846458857 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846458857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415797, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1487110397 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487110397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415903, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2057930032 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057930032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416983, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766387817 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766387817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417136, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1629611757 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629611757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418675, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 11,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2131866743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131866743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418933, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-539314191 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539314191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439128, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059451355 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059451355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439244, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-958955399 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958955399\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.440276, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 11,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-457604757 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457604757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.440396, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/?date=2025-05-26&equipe_id=9&region_id=", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard", "uri": "GET /", "controller": "Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor", "duration": "1.56s", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>region_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>equipe_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-26</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194296049 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-194296049\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-523523721 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJiR3lCcElabkc0Sm5uTkpHZjBaeGc9PSIsInZhbHVlIjoic3hka1d1NHI1VGpHcStPdW5aazNGWUU4SThHblhycTdmdjQwUHFld0NkYlh2d3lpMTJsNm1NSzliTHRUMnAreERqbExNcWFuMnY0Wk5hOWpnSHhhRWpCY1poWTNiRllCZ1V5SXpvcmR1YzZUZmRhTmFWUVJBMlREa2FLenBsdEYiLCJtYWMiOiI2MmNlYzQ3ZDMxMDgzNzdkMTE3ZTAzODkxMTdiYWU0YzFhMzg2NDQzYmI2YjJhYmNjNGUzYjg4NWVlZDcwNTMxIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6ImNIL2x0cmtVU3c1d1pLc1lQUHpreWc9PSIsInZhbHVlIjoiY0Ircy9IQkVtampzY2xaSnl2bUczZEVHRXpRNEFMdHZSeWVpRUdzY2RoMUJOZklGQlgzUjRZNjhLdTdubUtGcmRhUUxwekZQbkZVYW9tdGdja2pRV1kzZ3BlWWRPUkVaZHBla3UyaEJLUDRmYjR3NzlFaUMrZklWQUhSWngxRXIiLCJtYWMiOiJjNTg3YWI4M2FjM2FlM2FiMzlkMzVkYWNlNzc4OTZjOTZlMzc5OTM0MWQyM2RlYWEyY2EyZTVlYWU1NDQ2NTQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?region_id=&amp;equipe_id=9&amp;date=2025-05-25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523523721\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2025873773 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">11|fJAGQZo119SnWfhn9FJXssfvf2PrUlfpfx8jEF9wGJSlIASs9AiKyT8DxX1p|$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025873773\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-333778260 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 09:52:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlZaMjBFTjRLcWZYSXBEVExnSGxicFE9PSIsInZhbHVlIjoiRGNsR2ozN1ZwQnRKU2FndTcybkJ2cldGOXVwUHZyMmgrait2eHUvc1ZOQnJ0MS9mb0xUWVBmKzcyci8xQWpiQUdsYWkxUzlOaVdJYyttdHVSVThrbVp1R0czYkZHMnhFWnRQNUdGZ0xWd0VRUjloOU5ZSGFmc3poWkhWWXNvaXIiLCJtYWMiOiI2YjZkZWQ1MzlkNmEyMWE5M2M1NzM0YjliNjQ5ZDAwNGUyZWRhODgyNTZjYzdiMWY3MTZiNmIzNWM0NGFjYTIzIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 11:52:52 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"457 characters\">line_analyzer_session=eyJpdiI6IitjT2VCd3JjREVBekFpTXUxN2ZVdWc9PSIsInZhbHVlIjoiSDJiR01WYTVBNXNUTUJteXU2eUN0TUlFQ1hjc2RJU2J6Zmlzb0FlWERXVEFqa1A2N0d3MXZsYUhiaU9ybk0rVUljNzhudi90VEpJL0E4eDlTQk9TTWdBOXpobVhhT0FDemlvaHV1Uk5JM3JFQXVHTDF1MFEzZUkwa1ZKa0ZoeEwiLCJtYWMiOiI0M2MyOTkzZjgxM2IwOTZkNzY3ZjZkOTMzM2Y1YmY5ZWEzNDFiOTA2ZDlhZjk5NDVkODM0M2FlNjVhNDMwM2I2IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 11:52:52 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlZaMjBFTjRLcWZYSXBEVExnSGxicFE9PSIsInZhbHVlIjoiRGNsR2ozN1ZwQnRKU2FndTcybkJ2cldGOXVwUHZyMmgrait2eHUvc1ZOQnJ0MS9mb0xUWVBmKzcyci8xQWpiQUdsYWkxUzlOaVdJYyttdHVSVThrbVp1R0czYkZHMnhFWnRQNUdGZ0xWd0VRUjloOU5ZSGFmc3poWkhWWXNvaXIiLCJtYWMiOiI2YjZkZWQ1MzlkNmEyMWE5M2M1NzM0YjliNjQ5ZDAwNGUyZWRhODgyNTZjYzdiMWY3MTZiNmIzNWM0NGFjYTIzIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 11:52:52 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"429 characters\">line_analyzer_session=eyJpdiI6IitjT2VCd3JjREVBekFpTXUxN2ZVdWc9PSIsInZhbHVlIjoiSDJiR01WYTVBNXNUTUJteXU2eUN0TUlFQ1hjc2RJU2J6Zmlzb0FlWERXVEFqa1A2N0d3MXZsYUhiaU9ybk0rVUljNzhudi90VEpJL0E4eDlTQk9TTWdBOXpobVhhT0FDemlvaHV1Uk5JM3JFQXVHTDF1MFEzZUkwa1ZKa0ZoeEwiLCJtYWMiOiI0M2MyOTkzZjgxM2IwOTZkNzY3ZjZkOTMzM2Y1YmY5ZWEzNDFiOTA2ZDlhZjk5NDVkODM0M2FlNjVhNDMwM2I2IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 11:52:52 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333778260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1359835530 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?date=2025-05-26&amp;equipe_id=9&amp;region_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359835530\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/?date=2025-05-26&equipe_id=9&region_id=", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard"}, "badge": null}}