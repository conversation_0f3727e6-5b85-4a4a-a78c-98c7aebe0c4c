-- SQL Server Database Schema for 'nourtel'
-- Generated on 2025-05-16 11:48:06

-- Table: [dbo].[books]
CREATE TABLE [dbo].[books] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [title] nvarchar(255)  NOT NULL,
    [author] nvarchar(255)  NOT NULL,
    [description] nvarchar(MAX)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__books__3213E83FD1F8DC44] PRIMARY KEY ([id])
);


-- Table: [dbo].[breezy_sessions]
CREATE TABLE [dbo].[breezy_sessions] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [authenticatable_type] nvarchar(255)  NOT NULL,
    [authenticatable_id] bigint  NOT NULL,
    [panel_id] nvarchar(255)  NULL,
    [guard] nvarchar(255)  NULL,
    [ip_address] nvarchar(45)  NULL,
    [user_agent] nvarchar(MAX)  NULL,
    [expires_at] datetime  NULL,
    [two_factor_secret] nvarchar(MAX)  NULL,
    [two_factor_recovery_codes] nvarchar(MAX)  NULL,
    [two_factor_confirmed_at] datetime  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__breezy_s__3213E83FCB642871] PRIMARY KEY ([id])
);

CREATE INDEX [breezy_sessions_authenticatable_type_authenticatable_id_index] ON [dbo].[breezy_sessions] ([authenticatable_type] ASC, [authenticatable_id] ASC);

-- Table: [dbo].[cache]
CREATE TABLE [dbo].[cache] (
    [key] nvarchar(255)  NOT NULL,
    [value] nvarchar(MAX)  NOT NULL,
    [expiration] int  NOT NULL,
    CONSTRAINT [cache_key_primary] PRIMARY KEY ([key])
);


-- Table: [dbo].[cache_locks]
CREATE TABLE [dbo].[cache_locks] (
    [key] nvarchar(255)  NOT NULL,
    [owner] nvarchar(255)  NOT NULL,
    [expiration] int  NOT NULL,
    CONSTRAINT [cache_locks_key_primary] PRIMARY KEY ([key])
);


-- Table: [dbo].[configurations]
CREATE TABLE [dbo].[configurations] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [type] nvarchar(255)  NOT NULL,
    [login] nvarchar(255)  NOT NULL,
    [password] nvarchar(255)  NOT NULL,
    [passerelle] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__configur__3213E83FBA996702] PRIMARY KEY ([id])
);


-- Table: [dbo].[contacts]
CREATE TABLE [dbo].[contacts] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__contacts__3213E83FDAE2F0C0] PRIMARY KEY ([id])
);


-- Table: [dbo].[emplacements]
CREATE TABLE [dbo].[emplacements] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [type] nvarchar(255)  NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__emplacem__3213E83FD3A3CCDB] PRIMARY KEY ([id])
);


-- Table: [dbo].[equipe_agent]
CREATE TABLE [dbo].[equipe_agent] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [equipe_id] bigint  NOT NULL,
    [user_id] bigint  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__equipe_a__3213E83FBCE952E1] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[equipe_agent] ADD CONSTRAINT [equipe_agent_equipe_id_foreign] FOREIGN KEY ([equipe_id]) REFERENCES [dbo].[equipes] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE [dbo].[equipe_agent] ADD CONSTRAINT [equipe_agent_user_id_foreign] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
CREATE UNIQUE INDEX [equipe_agent_equipe_id_user_id_unique] ON [dbo].[equipe_agent] ([equipe_id] ASC, [user_id] ASC);

-- Table: [dbo].[equipe_region]
CREATE TABLE [dbo].[equipe_region] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [equipe_id] bigint  NOT NULL,
    [region_id] bigint  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__equipe_r__3213E83FF4BF113A] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[equipe_region] ADD CONSTRAINT [equipe_region_equipe_id_foreign] FOREIGN KEY ([equipe_id]) REFERENCES [dbo].[equipes] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE [dbo].[equipe_region] ADD CONSTRAINT [equipe_region_region_id_foreign] FOREIGN KEY ([region_id]) REFERENCES [dbo].[regions] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
CREATE UNIQUE INDEX [equipe_region_equipe_id_region_id_unique] ON [dbo].[equipe_region] ([equipe_id] ASC, [region_id] ASC);

-- Table: [dbo].[equipe_responsable]
CREATE TABLE [dbo].[equipe_responsable] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [equipe_id] bigint  NOT NULL,
    [user_id] bigint  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__equipe_r__3213E83FDEE5076C] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[equipe_responsable] ADD CONSTRAINT [equipe_responsable_equipe_id_foreign] FOREIGN KEY ([equipe_id]) REFERENCES [dbo].[equipes] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE [dbo].[equipe_responsable] ADD CONSTRAINT [equipe_responsable_user_id_foreign] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
CREATE UNIQUE INDEX [equipe_responsable_equipe_id_user_id_unique] ON [dbo].[equipe_responsable] ([equipe_id] ASC, [user_id] ASC);

-- Table: [dbo].[equipes]
CREATE TABLE [dbo].[equipes] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [nom] nvarchar(255)  NOT NULL,
    [created_by] bigint  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [deleted_at] datetime  NULL,
    CONSTRAINT [PK__equipes__3213E83F393CAAE5] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[equipes] ADD CONSTRAINT [equipes_created_by_foreign] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]) ON DELETE SET NULL ON UPDATE NO ACTION;

-- Table: [dbo].[exports]
CREATE TABLE [dbo].[exports] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [completed_at] datetime  NULL,
    [file_disk] nvarchar(255)  NOT NULL,
    [file_name] nvarchar(255)  NULL,
    [exporter] nvarchar(255)  NOT NULL,
    [processed_rows] int  NOT NULL DEFAULT ('0'),
    [total_rows] int  NOT NULL,
    [successful_rows] int  NOT NULL DEFAULT ('0'),
    [user_id] bigint  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__exports__3213E83F9B6F268D] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[exports] ADD CONSTRAINT [exports_user_id_foreign] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;

-- Table: [dbo].[failed_import_rows]
CREATE TABLE [dbo].[failed_import_rows] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [data] nvarchar(MAX)  NOT NULL,
    [import_id] bigint  NOT NULL,
    [validation_error] nvarchar(MAX)  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__failed_i__3213E83F4581D556] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[failed_import_rows] ADD CONSTRAINT [failed_import_rows_import_id_foreign] FOREIGN KEY ([import_id]) REFERENCES [dbo].[imports] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;

-- Table: [dbo].[failed_jobs]
CREATE TABLE [dbo].[failed_jobs] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [uuid] nvarchar(255)  NOT NULL,
    [connection] nvarchar(MAX)  NOT NULL,
    [queue] nvarchar(MAX)  NOT NULL,
    [payload] nvarchar(MAX)  NOT NULL,
    [exception] nvarchar(MAX)  NOT NULL,
    [failed_at] datetime  NOT NULL DEFAULT (getdate()),
    CONSTRAINT [PK__failed_j__3213E83FF394BEEA] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [failed_jobs_uuid_unique] ON [dbo].[failed_jobs] ([uuid] ASC);

-- Table: [dbo].[imports]
CREATE TABLE [dbo].[imports] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [completed_at] datetime  NULL,
    [file_name] nvarchar(255)  NOT NULL,
    [file_path] nvarchar(255)  NOT NULL,
    [importer] nvarchar(255)  NOT NULL,
    [processed_rows] int  NOT NULL DEFAULT ('0'),
    [total_rows] int  NOT NULL,
    [successful_rows] int  NOT NULL DEFAULT ('0'),
    [user_id] bigint  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__imports__3213E83FC3C24285] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[imports] ADD CONSTRAINT [imports_user_id_foreign] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;

-- Table: [dbo].[job_batches]
CREATE TABLE [dbo].[job_batches] (
    [id] nvarchar(255)  NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [total_jobs] int  NOT NULL,
    [pending_jobs] int  NOT NULL,
    [failed_jobs] int  NOT NULL,
    [failed_job_ids] nvarchar(MAX)  NOT NULL,
    [options] nvarchar(MAX)  NULL,
    [cancelled_at] int  NULL,
    [created_at] int  NOT NULL,
    [finished_at] int  NULL,
    CONSTRAINT [job_batches_id_primary] PRIMARY KEY ([id])
);


-- Table: [dbo].[jobs]
CREATE TABLE [dbo].[jobs] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [queue] nvarchar(255)  NOT NULL,
    [payload] nvarchar(MAX)  NOT NULL,
    [attempts] tinyint  NOT NULL,
    [reserved_at] int  NULL,
    [available_at] int  NOT NULL,
    [created_at] int  NOT NULL,
    CONSTRAINT [PK__jobs__3213E83FFA148C65] PRIMARY KEY ([id])
);

CREATE INDEX [jobs_queue_index] ON [dbo].[jobs] ([queue] ASC);

-- Table: [dbo].[media]
CREATE TABLE [dbo].[media] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [model_type] nvarchar(255)  NOT NULL,
    [model_id] bigint  NOT NULL,
    [uuid] uniqueidentifier  NULL,
    [collection_name] nvarchar(255)  NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [file_name] nvarchar(255)  NOT NULL,
    [mime_type] nvarchar(255)  NULL,
    [disk] nvarchar(255)  NOT NULL,
    [conversions_disk] nvarchar(255)  NULL,
    [size] bigint  NOT NULL,
    [manipulations] nvarchar(MAX)  NOT NULL,
    [custom_properties] nvarchar(MAX)  NOT NULL,
    [generated_conversions] nvarchar(MAX)  NOT NULL,
    [responsive_images] nvarchar(MAX)  NOT NULL,
    [order_column] int  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__media__3213E83F48BA9EA1] PRIMARY KEY ([id])
);

CREATE INDEX [media_model_type_model_id_index] ON [dbo].[media] ([model_type] ASC, [model_id] ASC);
CREATE INDEX [media_order_column_index] ON [dbo].[media] ([order_column] ASC);
CREATE UNIQUE INDEX [media_uuid_unique] ON [dbo].[media] ([uuid] ASC);

-- Table: [dbo].[mesures]
CREATE TABLE [dbo].[mesures] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [created_by] bigint  NOT NULL,
    [equipe_id] bigint  NOT NULL,
    [region_id] bigint  NOT NULL,
    [g_rx_power] decimal(10, 2)  NULL,
    [g_tx_power] decimal(10, 2)  NULL,
    [g_voltage] decimal(10, 2)  NULL,
    [g_bias_current] decimal(10, 2)  NULL,
    [g_pon_alarm_info] nvarchar(255)  NULL,
    [xdsl_mode] nvarchar(255)  NULL,
    [xdsl_channel_mode] nvarchar(255)  NULL,
    [xdsl_up_stream] decimal(10, 2)  NULL,
    [xdsl_down_stream] decimal(10, 2)  NULL,
    [xdsl_max_up_attainable_rate] decimal(10, 2)  NULL,
    [xdsl_max_down_attainable_rate] decimal(10, 2)  NULL,
    [xdsl_attenuation_up_rate] decimal(10, 2)  NULL,
    [xdsl_attenuation_down_rate] decimal(10, 2)  NULL,
    [xdsl_snr_margin_up_stream] decimal(10, 2)  NULL,
    [xdsl_snr_margin_down_stream] decimal(10, 2)  NULL,
    [xdsl_crc_errors] int  NULL,
    [mesure_type] nvarchar(255)  NOT NULL,
    [mesure_status] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [lat] nvarchar(255)  NULL,
    [lng] nvarchar(255)  NULL,
    [num_ligne] nvarchar(255)  NULL,
    [emplacement] nvarchar(255)  NULL,
    CONSTRAINT [PK__mesures__3213E83FB04FA435] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[mesures] ADD CONSTRAINT [mesures_created_by_foreign] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE [dbo].[mesures] ADD CONSTRAINT [mesures_equipe_id_foreign] FOREIGN KEY ([equipe_id]) REFERENCES [dbo].[equipes] ([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE [dbo].[mesures] ADD CONSTRAINT [mesures_region_id_foreign] FOREIGN KEY ([region_id]) REFERENCES [dbo].[regions] ([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- Table: [dbo].[migrations]
CREATE TABLE [dbo].[migrations] (
    [id] int IDENTITY(1,1) NOT NULL,
    [migration] nvarchar(255)  NOT NULL,
    [batch] int  NOT NULL,
    CONSTRAINT [PK__migratio__3213E83FEDCCD6F9] PRIMARY KEY ([id])
);


-- Table: [dbo].[model_has_permissions]
CREATE TABLE [dbo].[model_has_permissions] (
    [permission_id] bigint  NOT NULL,
    [model_type] nvarchar(255)  NOT NULL,
    [model_id] bigint  NOT NULL,
    CONSTRAINT [model_has_permissions_permission_model_type_primary] PRIMARY KEY ([permission_id], [model_id], [model_type])
);

ALTER TABLE [dbo].[model_has_permissions] ADD CONSTRAINT [model_has_permissions_permission_id_foreign] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
CREATE INDEX [model_has_permissions_model_id_model_type_index] ON [dbo].[model_has_permissions] ([model_id] ASC, [model_type] ASC);

-- Table: [dbo].[model_has_roles]
CREATE TABLE [dbo].[model_has_roles] (
    [role_id] bigint  NOT NULL,
    [model_type] nvarchar(255)  NOT NULL,
    [model_id] bigint  NOT NULL,
    CONSTRAINT [model_has_roles_role_model_type_primary] PRIMARY KEY ([role_id], [model_id], [model_type])
);

ALTER TABLE [dbo].[model_has_roles] ADD CONSTRAINT [model_has_roles_role_id_foreign] FOREIGN KEY ([role_id]) REFERENCES [dbo].[roles] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
CREATE INDEX [model_has_roles_model_id_model_type_index] ON [dbo].[model_has_roles] ([model_id] ASC, [model_type] ASC);

-- Table: [dbo].[notifications]
CREATE TABLE [dbo].[notifications] (
    [id] uniqueidentifier  NOT NULL,
    [type] nvarchar(255)  NOT NULL,
    [notifiable_type] nvarchar(255)  NOT NULL,
    [notifiable_id] bigint  NOT NULL,
    [data] nvarchar(MAX)  NOT NULL,
    [read_at] datetime  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [notifications_id_primary] PRIMARY KEY ([id])
);

CREATE INDEX [notifications_notifiable_type_notifiable_id_index] ON [dbo].[notifications] ([notifiable_type] ASC, [notifiable_id] ASC);

-- Table: [dbo].[password_reset_tokens]
CREATE TABLE [dbo].[password_reset_tokens] (
    [email] nvarchar(255)  NOT NULL,
    [token] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    CONSTRAINT [password_reset_tokens_email_primary] PRIMARY KEY ([email])
);


-- Table: [dbo].[permissions]
CREATE TABLE [dbo].[permissions] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [guard_name] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__permissi__3213E83FDE9C77C4] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [permissions_name_guard_name_unique] ON [dbo].[permissions] ([name] ASC, [guard_name] ASC);

-- Table: [dbo].[personal_access_tokens]
CREATE TABLE [dbo].[personal_access_tokens] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [tokenable_type] nvarchar(255)  NOT NULL,
    [tokenable_id] bigint  NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [token] nvarchar(64)  NOT NULL,
    [abilities] nvarchar(MAX)  NULL,
    [last_used_at] datetime  NULL,
    [expires_at] datetime  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__personal__3213E83F00D538F5] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [personal_access_tokens_token_unique] ON [dbo].[personal_access_tokens] ([token] ASC);
CREATE INDEX [personal_access_tokens_tokenable_type_tokenable_id_index] ON [dbo].[personal_access_tokens] ([tokenable_type] ASC, [tokenable_id] ASC);

-- Table: [dbo].[posts]
CREATE TABLE [dbo].[posts] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [title] nvarchar(255)  NOT NULL,
    [content] nvarchar(MAX)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__posts__3213E83F476065C4] PRIMARY KEY ([id])
);


-- Table: [dbo].[regions]
CREATE TABLE [dbo].[regions] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [nom] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [created_by] bigint  NULL,
    [deleted_at] datetime  NULL,
    CONSTRAINT [PK__regions__3213E83FF80E1644] PRIMARY KEY ([id])
);

ALTER TABLE [dbo].[regions] ADD CONSTRAINT [regions_created_by_foreign] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]) ON DELETE SET NULL ON UPDATE NO ACTION;

-- Table: [dbo].[role_has_permissions]
CREATE TABLE [dbo].[role_has_permissions] (
    [permission_id] bigint  NOT NULL,
    [role_id] bigint  NOT NULL,
    CONSTRAINT [role_has_permissions_permission_id_role_id_primary] PRIMARY KEY ([permission_id], [role_id])
);

ALTER TABLE [dbo].[role_has_permissions] ADD CONSTRAINT [role_has_permissions_permission_id_foreign] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE [dbo].[role_has_permissions] ADD CONSTRAINT [role_has_permissions_role_id_foreign] FOREIGN KEY ([role_id]) REFERENCES [dbo].[roles] ([id]) ON DELETE CASCADE ON UPDATE NO ACTION;

-- Table: [dbo].[roles]
CREATE TABLE [dbo].[roles] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [guard_name] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__roles__3213E83F2BCE1DAC] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [roles_name_guard_name_unique] ON [dbo].[roles] ([name] ASC, [guard_name] ASC);

-- Table: [dbo].[sessions]
CREATE TABLE [dbo].[sessions] (
    [id] nvarchar(255)  NOT NULL,
    [user_id] bigint  NULL,
    [ip_address] nvarchar(45)  NULL,
    [user_agent] nvarchar(MAX)  NULL,
    [payload] nvarchar(MAX)  NOT NULL,
    [last_activity] int  NOT NULL,
    CONSTRAINT [sessions_id_primary] PRIMARY KEY ([id])
);

CREATE INDEX [sessions_last_activity_index] ON [dbo].[sessions] ([last_activity] ASC);
CREATE INDEX [sessions_user_id_index] ON [dbo].[sessions] ([user_id] ASC);

-- Table: [dbo].[settings]
CREATE TABLE [dbo].[settings] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [group] nvarchar(255)  NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [locked] bit  NOT NULL DEFAULT ('0'),
    [payload] nvarchar(MAX)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__settings__3213E83F7EF82864] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [settings_group_name_unique] ON [dbo].[settings] ([group] ASC, [name] ASC);

-- Table: [dbo].[socialite_users]
CREATE TABLE [dbo].[socialite_users] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [user_id] bigint  NOT NULL,
    [provider] nvarchar(255)  NOT NULL,
    [provider_id] nvarchar(255)  NOT NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__socialit__3213E83F5C32EC1D] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [socialite_users_provider_provider_id_unique] ON [dbo].[socialite_users] ([provider] ASC, [provider_id] ASC);

-- Table: [dbo].[users]
CREATE TABLE [dbo].[users] (
    [id] bigint IDENTITY(1,1) NOT NULL,
    [name] nvarchar(255)  NOT NULL,
    [email] nvarchar(255)  NOT NULL,
    [email_verified_at] datetime  NULL,
    [password] nvarchar(255)  NULL,
    [remember_token] nvarchar(100)  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [avatar_url] nvarchar(255)  NULL,
    [theme] nvarchar(255)  NULL DEFAULT ('default'),
    [theme_color] nvarchar(255)  NULL,
    [mobile_token] nvarchar(MAX)  NULL,
    CONSTRAINT [PK__users__3213E83F4A0D3358] PRIMARY KEY ([id])
);

CREATE UNIQUE INDEX [users_email_unique] ON [dbo].[users] ([email] ASC);

