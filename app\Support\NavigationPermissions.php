<?php

namespace App\Support;

/**
 * Class NavigationPermissions
 * 
 * This class defines custom permissions for navigation items
 */
class NavigationPermissions
{
    // Navigation permission constants
    public const VIEW_PROFIL_NAVIGATION = 'view_profil_navigation';
    public const VIEW_EQUIPES_NAVIGATION = 'view_equipes_navigation';
    public const VIEW_CONFIGURATIONS_NAVIGATION = 'view_configurations_navigation';
    public const VIEW_EMPLACEMENTS_NAVIGATION = 'view_emplacements_navigation';
    public const VIEW_MESURES_NAVIGATION = 'view_mesures_navigation';
    
    /**
     * Get all navigation permissions
     * 
     * @return array
     */
    public static function all(): array
    {
        return [
            self::VIEW_PROFIL_NAVIGATION => 'Voir le menu Profil',
            self::VIEW_EQUIPES_NAVIGATION => 'Voir le menu Équipes',
            self::VIEW_CONFIGURATIONS_NAVIGATION => 'Voir le menu Configurations',
            self::VIEW_EMPLACEMENTS_NAVIGATION => 'Voir le menu Emplacements',
            self::VIEW_MESURES_NAVIGATION => 'Voir le menu Mesures',
        ];
    }
}
