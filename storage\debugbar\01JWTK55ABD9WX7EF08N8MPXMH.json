{"__meta": {"id": "01JWTK55ABD9WX7EF08N8MPXMH", "datetime": "2025-06-03 09:49:56", "utime": **********.940082, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748944195.765822, "end": **********.940096, "duration": 1.17427396774292, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1748944195.765822, "relative_start": 0, "end": **********.514883, "relative_end": **********.514883, "duration": 0.***************, "duration_str": "749ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.514895, "relative_start": 0.****************, "end": **********.940097, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.675025, "relative_start": 0.***************, "end": **********.676861, "relative_end": **********.676861, "duration": 0.0018360614776611328, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::components.topbar.database-notifications-trigger", "start": **********.923957, "relative_start": 1.***************, "end": **********.923957, "relative_end": **********.923957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.925572, "relative_start": 1.***************, "end": **********.925572, "relative_end": **********.925572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.928586, "relative_start": 1.162764072418213, "end": **********.928586, "relative_end": **********.928586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::simple-tailwind", "start": **********.930382, "relative_start": 1.164560079574585, "end": **********.930382, "relative_end": **********.930382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.932054, "relative_start": 1.1662321090698242, "end": **********.932054, "relative_end": **********.932054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.933817, "relative_start": 1.1679949760437012, "end": **********.933817, "relative_end": **********.933817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.935524, "relative_start": 1.1697020530700684, "end": **********.935524, "relative_end": **********.935524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.935987, "relative_start": 1.1701650619506836, "end": **********.935987, "relative_end": **********.935987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.936409, "relative_start": 1.1705870628356934, "end": **********.936409, "relative_end": **********.936409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.description", "start": **********.93665, "relative_start": 1.170828104019165, "end": **********.93665, "relative_end": **********.93665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.937895, "relative_start": 1.1720731258392334, "end": **********.93856, "relative_end": **********.93856, "duration": 0.0006649494171142578, "duration_str": "665μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 45334712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "filament-panels::components.topbar.database-notifications-trigger", "param_count": null, "params": [], "start": **********.923933, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/topbar/database-notifications-trigger.blade.phpfilament-panels::components.topbar.database-notifications-trigger", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Fdatabase-notifications-trigger.blade.php&line=1", "ajax": false, "filename": "database-notifications-trigger.blade.php", "line": "?"}}, {"name": "filament::components.icon-button", "param_count": null, "params": [], "start": **********.925558, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": **********.928557, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "livewire::simple-tailwind", "param_count": null, "params": [], "start": **********.930365, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/simple-tailwind.blade.phplivewire::simple-tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Fsimple-tailwind.blade.php&line=1", "ajax": false, "filename": "simple-tailwind.blade.php", "line": "?"}}, {"name": "filament::components.modal.index", "param_count": null, "params": [], "start": **********.93204, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.icon-button", "param_count": null, "params": [], "start": **********.933806, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": **********.935514, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": **********.935976, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.modal.heading", "param_count": null, "params": [], "start": **********.936401, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.modal.description", "param_count": null, "params": [], "start": **********.936639, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/description.blade.phpfilament::components.modal.description", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fdescription.blade.php&line=1", "ajax": false, "filename": "description.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.20537, "accumulated_duration_str": "205ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC'", "type": "query", "params": [], "bindings": ["KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.682297, "duration": 0.07015, "duration_str": "70.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 34.158}, {"sql": "select top 1 * from [users] where [id] = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.76055, "duration": 0.019649999999999997, "duration_str": "19.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 34.158, "width_percent": 9.568}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7847202, "duration": 0.019059999999999997, "duration_str": "19.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 43.726, "width_percent": 9.281}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.806884, "duration": 0.02093, "duration_str": "20.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 53.007, "width_percent": 10.191}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.829059, "duration": 0.01832, "duration_str": "18.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 63.198, "width_percent": 8.92}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 11 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [11, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.8486922, "duration": 0.01959, "duration_str": "19.59ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 72.119, "width_percent": 9.539}, {"sql": "select top 51 * from [notifications] where [notifications].[notifiable_type] = 'App\\Models\\User' and [notifications].[notifiable_id] = 11 and [notifications].[notifiable_id] is not null and json_value([data], '$.\"format\"') = 'filament' order by [created_at] desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 11, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.880686, "duration": 0.01844, "duration_str": "18.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "nourtel", "explain": null, "start_percent": 81.657, "width_percent": 8.979}, {"sql": "select count(*) as aggregate from [notifications] where [notifications].[notifiable_type] = 'App\\Models\\User' and [notifications].[notifiable_id] = 11 and [notifications].[notifiable_id] is not null and json_value([data], '$.\"format\"') = 'filament' and [read_at] is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 11, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.902295, "duration": 0.01923, "duration_str": "19.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 90.636, "width_percent": 9.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #NUT8n8qDnooa7AnbyTu1": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"NUT8n8qDnooa7AnbyTu1\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.17s", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1376031928 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1376031928\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1819426540 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;NUT8n8qDnooa7AnbyTu1&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;\\/&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;fr&quot;},&quot;checksum&quot;:&quot;9a6214cdb0ade842969540d5ecf9d40fb48d2cb4f9e017e6d82a3360161832b3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819426540\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1219479178 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9DMy9STVJlcTBuZVM0VCs5WmxsMGc9PSIsInZhbHVlIjoiMUUwUWovanZEc1JPUzdRdEdhRGN3VU9pT2VpUDQ1ZFg4d1F4Myt0Z0ZXU2Zhbm02YlcxMytIWVpoUjh3NEFvVkNRSk1rczlMSkkvQk1SbTNVQ0N1S3JaZzAxY3dwN2tJcHpuQW1kR0x6SnJwNWw1bEJTQU5MbFNEaHhXdHVUTUEiLCJtYWMiOiI4NGM1Y2Y2ZWU4MmVmNTE2ZTM1NjJjOGNlNTVkYTNiN2MzODgxNGIxNTg3NWNkM2MzMWUxYmI4YWQxZWM0MTQ1IiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6ImFVRytNVVdIQmpxMEV3d3MyMWVlNXc9PSIsInZhbHVlIjoiUzBob0pzd3ZVWlBNbGlBcVAyWjd0azhLNDhkeUtuT04yOVl2MllNeEdUaFpjSlNuQmY0YjNCaFA0UC9DWDJVSFdCMitzU3paMkNOMHZGK0tmQ1NMNGNOUmxJK0Qxak42MUFRazR2Ni8ybVRNaVRUNXJzUnR5OEFNSmU3LzJmanAiLCJtYWMiOiI1Y2NiZWI0MDU4YWE5ZGNlOTM5NzM4MDIxZDY2OTBmYmQ5NTJkOTQwODBhMDI0Y2I1MDQ3MDhmYTIxMjU2YTVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?region_id=&amp;equipe_id=9&amp;date=2025-05-25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://nourtel.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">501</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219479178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1783651805 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">11|fJAGQZo119SnWfhn9FJXssfvf2PrUlfpfx8jEF9wGJSlIASs9AiKyT8DxX1p|$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783651805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2013789725 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 09:49:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013789725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2020393257 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?date=2025-05-25&amp;equipe_id=9&amp;region_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020393257\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}