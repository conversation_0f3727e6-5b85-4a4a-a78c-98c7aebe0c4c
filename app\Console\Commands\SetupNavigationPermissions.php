<?php

namespace App\Console\Commands;

use Database\Seeders\NavigationPermissionsSeeder;
use Illuminate\Console\Command;

class SetupNavigationPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-navigation-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup navigation permissions and assign them to roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up navigation permissions...');
        
        $seeder = new NavigationPermissionsSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Navigation permissions setup completed!');
        
        return Command::SUCCESS;
    }
}
