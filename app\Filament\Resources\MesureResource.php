<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MesureResource\Pages;
use App\Models\Mesure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MesureResource extends Resource
{
    protected static ?string $model = Mesure::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 8;

    // Let Shield handle the permissions
    // No need to override shouldRegisterNavigation

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('created_by')
                    ->relationship('creator', 'name')
                    ->required()
                    ->disabled()
                    ->default(Auth::id()),

                Forms\Components\Select::make('equipe_id')
                    ->relationship('equipe', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->equipe_id),

                Forms\Components\Select::make('region_id')
                    ->relationship('region', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->region_id),

                Forms\Components\Section::make('GPON Measurements')
                    ->schema([
                        Forms\Components\TextInput::make('g_rx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_tx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_voltage')
                            ->numeric()
                            ->suffix('V'),
                        Forms\Components\TextInput::make('g_bias_current')
                            ->numeric()
                            ->suffix('mA'),
                        Forms\Components\TextInput::make('g_pon_alarm_info')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('xDSL Measurements')
                    ->schema([
                        Forms\Components\TextInput::make('xdsl_mode')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('xdsl_channel_mode')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('xdsl_up_stream')
                            ->numeric()
                            ->suffix('kbps'),
                        Forms\Components\TextInput::make('xdsl_down_stream')
                            ->numeric()
                            ->suffix('kbps'),
                        Forms\Components\TextInput::make('xdsl_max_up_attainable_rate')
                            ->numeric()
                            ->suffix('kbps'),
                        Forms\Components\TextInput::make('xdsl_max_down_attainable_rate')
                            ->numeric()
                            ->suffix('kbps'),
                        Forms\Components\TextInput::make('xdsl_attenuation_up_rate')
                            ->numeric()
                            ->suffix('dB'),
                        Forms\Components\TextInput::make('xdsl_attenuation_down_rate')
                            ->numeric()
                            ->suffix('dB'),
                        Forms\Components\TextInput::make('xdsl_snr_margin_up_stream')
                            ->numeric()
                            ->suffix('dB'),
                        Forms\Components\TextInput::make('xdsl_snr_margin_down_stream')
                            ->numeric()
                            ->suffix('dB'),
                        Forms\Components\TextInput::make('xdsl_crc_errors')
                            ->numeric(),
                    ])->columns(2),

                Forms\Components\Section::make('Status')
                    ->schema([
                        Forms\Components\Select::make('mesure_type')
                            ->options([
                                'gpon' => 'GPON',
                                'xdsl' => 'xDSL',
                                'both' => 'Both',
                            ])
                            ->required(),
                        Forms\Components\Select::make('mesure_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                            ])
                            ->required(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->sortable(),
                Tables\Columns\TextColumn::make('equipe.nom')
                    ->sortable(),
                Tables\Columns\TextColumn::make('region.nom')
                    ->sortable(),
                Tables\Columns\TextColumn::make('mesure_type')
                    ->badge()
                    ->color(fn (?string $state): string => match ($state) {
                        'gpon' => 'info',
                        'xdsl' => 'warning',
                        'both' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('mesure_status')
                    ->badge()
                    ->color(fn (?string $state): string => match ($state) {
                        'pending' => 'warning',
                        'completed' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('mesure_type')
                    ->options([
                        'gpon' => 'GPON',
                        'xdsl' => 'xDSL',
                        'both' => 'Both',
                    ]),
                Tables\Filters\SelectFilter::make('mesure_status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMesures::route('/'),
            'create' => Pages\CreateMesure::route('/create'),
            'edit' => Pages\EditMesure::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (Auth::user()->equipe_id && Auth::user()->region_id) {
            $query->where('equipe_id', Auth::user()->equipe_id)
                  ->where('region_id', Auth::user()->region_id);
        }

        return $query;
    }
}