{"__meta": {"id": "01JWTMWZYVR2Z339MJD4VE5ACZ", "datetime": "2025-06-03 10:20:26", "utime": **********.459701, "method": "GET", "uri": "/x-d-s-l-services", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748946023.472489, "end": **********.459715, "duration": 2.9872257709503174, "duration_str": "2.99s", "measures": [{"label": "Booting", "start": 1748946023.472489, "relative_start": 0, "end": **********.159419, "relative_end": **********.159419, "duration": 0.****************, "duration_str": "687ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.159434, "relative_start": 0.****************, "end": **********.459717, "relative_end": 2.1457672119140625e-06, "duration": 2.****************, "duration_str": "2.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.362228, "relative_start": 0.****************, "end": **********.36729, "relative_end": **********.36729, "duration": 0.005062103271484375, "duration_str": "5.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::resources.pages.list-records", "start": **********.804219, "relative_start": 1.****************, "end": **********.804219, "relative_end": **********.804219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.resources.tabs", "start": **********.805522, "relative_start": 1.****************, "end": **********.805522, "relative_end": **********.805522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.229462, "relative_start": 1.7569727897644043, "end": **********.229462, "relative_end": **********.229462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.229853, "relative_start": 1.7573637962341309, "end": **********.229853, "relative_end": **********.229853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.230988, "relative_start": 1.7584989070892334, "end": **********.230988, "relative_end": **********.230988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.231934, "relative_start": 1.7594449520111084, "end": **********.231934, "relative_end": **********.231934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.235302, "relative_start": 1.762812852859497, "end": **********.235302, "relative_end": **********.235302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.235704, "relative_start": 1.7632148265838623, "end": **********.235704, "relative_end": **********.235704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.236655, "relative_start": 1.7641658782958984, "end": **********.236655, "relative_end": **********.236655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": **********.2371, "relative_start": 1.764610767364502, "end": **********.2371, "relative_end": **********.2371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.23872, "relative_start": 1.766230821609497, "end": **********.23872, "relative_end": **********.23872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.240109, "relative_start": 1.7676198482513428, "end": **********.240109, "relative_end": **********.240109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.240407, "relative_start": 1.7679178714752197, "end": **********.240407, "relative_end": **********.240407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.243885, "relative_start": 1.7713959217071533, "end": **********.243885, "relative_end": **********.243885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.244143, "relative_start": 1.7716538906097412, "end": **********.244143, "relative_end": **********.244143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.244958, "relative_start": 1.7724688053131104, "end": **********.244958, "relative_end": **********.244958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.246258, "relative_start": 1.7737689018249512, "end": **********.246258, "relative_end": **********.246258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.247393, "relative_start": 1.7749037742614746, "end": **********.247393, "relative_end": **********.247393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.247631, "relative_start": 1.775141954421997, "end": **********.247631, "relative_end": **********.247631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.248197, "relative_start": 1.7757079601287842, "end": **********.248197, "relative_end": **********.248197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.249082, "relative_start": 1.7765929698944092, "end": **********.249082, "relative_end": **********.249082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.249376, "relative_start": 1.7768869400024414, "end": **********.249376, "relative_end": **********.249376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.249746, "relative_start": 1.777256965637207, "end": **********.249746, "relative_end": **********.249746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.251578, "relative_start": 1.7790889739990234, "end": **********.251578, "relative_end": **********.251578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.251786, "relative_start": 1.779296875, "end": **********.251786, "relative_end": **********.251786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.252561, "relative_start": 1.7800719738006592, "end": **********.252561, "relative_end": **********.252561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.253871, "relative_start": 1.7813818454742432, "end": **********.253871, "relative_end": **********.253871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.254366, "relative_start": 1.781876802444458, "end": **********.254366, "relative_end": **********.254366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.254743, "relative_start": 1.7822539806365967, "end": **********.254743, "relative_end": **********.254743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.255073, "relative_start": 1.7825839519500732, "end": **********.255073, "relative_end": **********.255073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.255478, "relative_start": 1.7829887866973877, "end": **********.255478, "relative_end": **********.255478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.258926, "relative_start": 1.7864367961883545, "end": **********.258926, "relative_end": **********.258926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.259751, "relative_start": 1.787261962890625, "end": **********.259751, "relative_end": **********.259751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.260429, "relative_start": 1.7879397869110107, "end": **********.260429, "relative_end": **********.260429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.261068, "relative_start": 1.788578987121582, "end": **********.261068, "relative_end": **********.261068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.261631, "relative_start": 1.7891418933868408, "end": **********.261631, "relative_end": **********.261631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.262277, "relative_start": 1.789787769317627, "end": **********.262277, "relative_end": **********.262277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.262995, "relative_start": 1.7905058860778809, "end": **********.262995, "relative_end": **********.262995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.263576, "relative_start": 1.7910869121551514, "end": **********.263576, "relative_end": **********.263576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.264427, "relative_start": 1.7919378280639648, "end": **********.264427, "relative_end": **********.264427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.28561, "relative_start": 1.8131208419799805, "end": **********.28561, "relative_end": **********.28561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.288127, "relative_start": 1.8156378269195557, "end": **********.288127, "relative_end": **********.288127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.312322, "relative_start": 1.8398327827453613, "end": **********.312322, "relative_end": **********.312322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.314712, "relative_start": 1.8422229290008545, "end": **********.314712, "relative_end": **********.314712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.430084, "relative_start": 1.957594871520996, "end": **********.430084, "relative_end": **********.430084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.449025, "relative_start": 1.9765357971191406, "end": **********.449025, "relative_end": **********.449025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.45135, "relative_start": 1.978860855102539, "end": **********.45135, "relative_end": **********.45135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.527117, "relative_start": 2.0546278953552246, "end": **********.527117, "relative_end": **********.527117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.552266, "relative_start": 2.0797767639160156, "end": **********.552266, "relative_end": **********.552266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.555, "relative_start": 2.0825109481811523, "end": **********.555, "relative_end": **********.555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.63595, "relative_start": 2.1634609699249268, "end": **********.63595, "relative_end": **********.63595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.665513, "relative_start": 2.193023920059204, "end": **********.665513, "relative_end": **********.665513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.670019, "relative_start": 2.1975297927856445, "end": **********.670019, "relative_end": **********.670019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.754859, "relative_start": 2.28236985206604, "end": **********.754859, "relative_end": **********.754859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.786292, "relative_start": 2.31380295753479, "end": **********.786292, "relative_end": **********.786292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.79076, "relative_start": 2.3182709217071533, "end": **********.79076, "relative_end": **********.79076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.868401, "relative_start": 2.395911931991577, "end": **********.868401, "relative_end": **********.868401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.889211, "relative_start": 2.416721820831299, "end": **********.889211, "relative_end": **********.889211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.892315, "relative_start": 2.419825792312622, "end": **********.892315, "relative_end": **********.892315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.967047, "relative_start": 2.4945578575134277, "end": **********.967047, "relative_end": **********.967047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.993402, "relative_start": 2.5209128856658936, "end": **********.993402, "relative_end": **********.993402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.995747, "relative_start": 2.5232579708099365, "end": **********.995747, "relative_end": **********.995747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.070522, "relative_start": 2.5980329513549805, "end": **********.070522, "relative_end": **********.070522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.097102, "relative_start": 2.624612808227539, "end": **********.097102, "relative_end": **********.097102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.099781, "relative_start": 2.6272919178009033, "end": **********.099781, "relative_end": **********.099781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.17317, "relative_start": 2.70068097114563, "end": **********.17317, "relative_end": **********.17317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.199777, "relative_start": 2.727287769317627, "end": **********.199777, "relative_end": **********.199777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.203145, "relative_start": 2.7306559085845947, "end": **********.203145, "relative_end": **********.203145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.205614, "relative_start": 2.7331249713897705, "end": **********.205614, "relative_end": **********.205614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.index", "start": **********.207534, "relative_start": 2.7350449562072754, "end": **********.207534, "relative_end": **********.207534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.20938, "relative_start": 2.7368907928466797, "end": **********.20938, "relative_end": **********.20938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.209718, "relative_start": 2.7372288703918457, "end": **********.209718, "relative_end": **********.209718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.210379, "relative_start": 2.7378897666931152, "end": **********.210379, "relative_end": **********.210379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.210565, "relative_start": 2.7380759716033936, "end": **********.210565, "relative_end": **********.210565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.2112, "relative_start": 2.738710880279541, "end": **********.2112, "relative_end": **********.2112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.214035, "relative_start": 2.7415459156036377, "end": **********.214035, "relative_end": **********.214035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.214735, "relative_start": 2.74224591255188, "end": **********.214735, "relative_end": **********.214735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.215233, "relative_start": 2.742743968963623, "end": **********.215233, "relative_end": **********.215233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.215592, "relative_start": 2.743102788925171, "end": **********.215592, "relative_end": **********.215592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.215894, "relative_start": 2.7434048652648926, "end": **********.215894, "relative_end": **********.215894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.216138, "relative_start": 2.7436487674713135, "end": **********.216138, "relative_end": **********.216138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.216433, "relative_start": 2.743943929672241, "end": **********.216433, "relative_end": **********.216433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.216709, "relative_start": 2.7442197799682617, "end": **********.216709, "relative_end": **********.216709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.216954, "relative_start": 2.744464874267578, "end": **********.216954, "relative_end": **********.216954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.217178, "relative_start": 2.7446889877319336, "end": **********.217178, "relative_end": **********.217178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.220698, "relative_start": 2.748208999633789, "end": **********.220698, "relative_end": **********.220698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.222289, "relative_start": 2.749799966812134, "end": **********.222289, "relative_end": **********.222289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.223473, "relative_start": 2.750983953475952, "end": **********.223473, "relative_end": **********.223473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.224572, "relative_start": 2.7520828247070312, "end": **********.224572, "relative_end": **********.224572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.225632, "relative_start": 2.753142833709717, "end": **********.225632, "relative_end": **********.225632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.228147, "relative_start": 2.755657911300659, "end": **********.228147, "relative_end": **********.228147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.filters-header", "start": **********.302669, "relative_start": 2.8301799297332764, "end": **********.302669, "relative_end": **********.302669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.304666, "relative_start": 2.832176923751831, "end": **********.304666, "relative_end": **********.304666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.327267, "relative_start": 2.8547778129577637, "end": **********.327267, "relative_end": **********.327267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.327958, "relative_start": 2.855468988418579, "end": **********.327958, "relative_end": **********.327958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.351233, "relative_start": 2.878743886947632, "end": **********.351233, "relative_end": **********.351233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.353272, "relative_start": 2.8807828426361084, "end": **********.353272, "relative_end": **********.353272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.354476, "relative_start": 2.8819868564605713, "end": **********.354476, "relative_end": **********.354476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.355232, "relative_start": 2.8827428817749023, "end": **********.355232, "relative_end": **********.355232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.356264, "relative_start": 2.883774995803833, "end": **********.356264, "relative_end": **********.356264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.359214, "relative_start": 2.8867249488830566, "end": **********.359214, "relative_end": **********.359214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.360563, "relative_start": 2.8880739212036133, "end": **********.360563, "relative_end": **********.360563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.361584, "relative_start": 2.889094829559326, "end": **********.361584, "relative_end": **********.361584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.364924, "relative_start": 2.89243483543396, "end": **********.364924, "relative_end": **********.364924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.36575, "relative_start": 2.893260955810547, "end": **********.36575, "relative_end": **********.36575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.366387, "relative_start": 2.893897771835327, "end": **********.366387, "relative_end": **********.366387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.368331, "relative_start": 2.8958418369293213, "end": **********.368331, "relative_end": **********.368331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.369326, "relative_start": 2.896836996078491, "end": **********.369326, "relative_end": **********.369326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.369861, "relative_start": 2.897371768951416, "end": **********.369861, "relative_end": **********.369861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.370435, "relative_start": 2.8979458808898926, "end": **********.370435, "relative_end": **********.370435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.370785, "relative_start": 2.8982958793640137, "end": **********.370785, "relative_end": **********.370785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.371664, "relative_start": 2.899174928665161, "end": **********.371664, "relative_end": **********.371664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.372331, "relative_start": 2.899841785430908, "end": **********.372331, "relative_end": **********.372331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.374591, "relative_start": 2.902101993560791, "end": **********.374591, "relative_end": **********.374591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.375388, "relative_start": 2.9028987884521484, "end": **********.375388, "relative_end": **********.375388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.376732, "relative_start": 2.904242992401123, "end": **********.376732, "relative_end": **********.376732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.377722, "relative_start": 2.9052329063415527, "end": **********.377722, "relative_end": **********.377722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.379017, "relative_start": 2.9065279960632324, "end": **********.379017, "relative_end": **********.379017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.379944, "relative_start": 2.9074549674987793, "end": **********.379944, "relative_end": **********.379944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.381482, "relative_start": 2.9089927673339844, "end": **********.381482, "relative_end": **********.381482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.382172, "relative_start": 2.9096829891204834, "end": **********.382172, "relative_end": **********.382172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.382408, "relative_start": 2.909918785095215, "end": **********.382408, "relative_end": **********.382408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.384027, "relative_start": 2.9115378856658936, "end": **********.384027, "relative_end": **********.384027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar-logo", "start": **********.404205, "relative_start": 2.931715965270996, "end": **********.404205, "relative_end": **********.404205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.404698, "relative_start": 2.932208776473999, "end": **********.404698, "relative_end": **********.404698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.405495, "relative_start": 2.9330058097839355, "end": **********.405495, "relative_end": **********.405495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.405941, "relative_start": 2.9334518909454346, "end": **********.405941, "relative_end": **********.405941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.406882, "relative_start": 2.9343929290771484, "end": **********.406882, "relative_end": **********.406882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.407574, "relative_start": 2.935084819793701, "end": **********.407574, "relative_end": **********.407574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.408486, "relative_start": 2.9359967708587646, "end": **********.408486, "relative_end": **********.408486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.409042, "relative_start": 2.9365527629852295, "end": **********.409042, "relative_end": **********.409042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.409437, "relative_start": 2.936947822570801, "end": **********.409437, "relative_end": **********.409437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.409854, "relative_start": 2.9373648166656494, "end": **********.409854, "relative_end": **********.409854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.410386, "relative_start": 2.937896966934204, "end": **********.410386, "relative_end": **********.410386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.411074, "relative_start": 2.938584804534912, "end": **********.411074, "relative_end": **********.411074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.411795, "relative_start": 2.9393057823181152, "end": **********.411795, "relative_end": **********.411795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.412203, "relative_start": 2.939713954925537, "end": **********.412203, "relative_end": **********.412203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.412632, "relative_start": 2.940142869949341, "end": **********.412632, "relative_end": **********.412632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.413092, "relative_start": 2.9406027793884277, "end": **********.413092, "relative_end": **********.413092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.413914, "relative_start": 2.94142484664917, "end": **********.413914, "relative_end": **********.413914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.414861, "relative_start": 2.9423718452453613, "end": **********.414861, "relative_end": **********.414861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.41592, "relative_start": 2.9434309005737305, "end": **********.41592, "relative_end": **********.41592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.41706, "relative_start": 2.944570779800415, "end": **********.41706, "relative_end": **********.41706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.417616, "relative_start": 2.94512677192688, "end": **********.417616, "relative_end": **********.417616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.418344, "relative_start": 2.945854902267456, "end": **********.418344, "relative_end": **********.418344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.419197, "relative_start": 2.9467079639434814, "end": **********.419197, "relative_end": **********.419197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.420341, "relative_start": 2.9478518962860107, "end": **********.420341, "relative_end": **********.420341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.42091, "relative_start": 2.948420763015747, "end": **********.42091, "relative_end": **********.42091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.4216, "relative_start": 2.949110984802246, "end": **********.4216, "relative_end": **********.4216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.422566, "relative_start": 2.9500768184661865, "end": **********.422566, "relative_end": **********.422566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.422877, "relative_start": 2.950387954711914, "end": **********.422877, "relative_end": **********.422877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.426329, "relative_start": 2.9538397789001465, "end": **********.426329, "relative_end": **********.426329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.426815, "relative_start": 2.9543259143829346, "end": **********.426815, "relative_end": **********.426815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.430134, "relative_start": 2.9576449394226074, "end": **********.430239, "relative_end": **********.430239, "duration": 0.0001049041748046875, "duration_str": "105μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.457579, "relative_start": 2.9850897789001465, "end": **********.457732, "relative_end": **********.457732, "duration": 0.00015306472778320312, "duration_str": "153μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52381264, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 153, "nb_templates": 153, "templates": [{"name": "1x filament-panels::resources.pages.list-records", "param_count": null, "params": [], "start": **********.804203, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/resources/pages/list-records.blade.phpfilament-panels::resources.pages.list-records", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fresources%2Fpages%2Flist-records.blade.php&line=1", "ajax": false, "filename": "list-records.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::resources.pages.list-records"}, {"name": "1x filament-panels::components.resources.tabs", "param_count": null, "params": [], "start": **********.80551, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/resources/tabs.blade.phpfilament-panels::components.resources.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fresources%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.resources.tabs"}, {"name": "4x filament::components.input.index", "param_count": null, "params": [], "start": **********.22945, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.input.index"}, {"name": "6x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.229841, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament::components.input.wrapper"}, {"name": "31x filament::components.icon", "param_count": null, "params": [], "start": **********.230978, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 31, "name_original": "filament::components.icon"}, {"name": "4x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.231924, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.loading-indicator"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.235291, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "7x filament::components.icon-button", "param_count": null, "params": [], "start": **********.235695, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 7, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": **********.237091, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "1x filament::components.link", "param_count": null, "params": [], "start": **********.23871, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.link"}, {"name": "2x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.244949, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "5x filament::components.grid.column", "param_count": null, "params": [], "start": **********.246248, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.grid.column"}, {"name": "3x filament::components.grid.index", "param_count": null, "params": [], "start": **********.249368, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.grid.index"}, {"name": "1x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.25255, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "2x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.255468, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.index"}, {"name": "10x filament.resources.xdsl-service-resource.pages.status-column", "param_count": null, "params": [], "start": **********.285597, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/status-column.blade.phpfilament.resources.xdsl-service-resource.pages.status-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Fstatus-column.blade.php&line=1", "ajax": false, "filename": "status-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.status-column"}, {"name": "10x filament.resources.xdsl-service-resource.pages.location-column", "param_count": null, "params": [], "start": **********.288113, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.phpfilament.resources.xdsl-service-resource.pages.location-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=1", "ajax": false, "filename": "location-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.location-column"}, {"name": "8x __components::d14a7696a048e3b36f9bc9179acb02b3", "param_count": null, "params": [], "start": **********.430072, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/d14a7696a048e3b36f9bc9179acb02b3.blade.php__components::d14a7696a048e3b36f9bc9179acb02b3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fd14a7696a048e3b36f9bc9179acb02b3.blade.php&line=1", "ajax": false, "filename": "d14a7696a048e3b36f9bc9179acb02b3.blade.php", "line": "?"}, "render_count": 8, "name_original": "__components::d14a7696a048e3b36f9bc9179acb02b3"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.205492, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x filament::components.pagination.index", "param_count": null, "params": [], "start": **********.207519, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/index.blade.phpfilament::components.pagination.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.pagination.index"}, {"name": "2x filament::components.input.select", "param_count": null, "params": [], "start": **********.209363, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/select.blade.phpfilament::components.input.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.select"}, {"name": "1x filament::components.button.index", "param_count": null, "params": [], "start": **********.21119, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.button.index"}, {"name": "8x filament::components.pagination.item", "param_count": null, "params": [], "start": **********.214723, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/item.blade.phpfilament::components.pagination.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 8, "name_original": "filament::components.pagination.item"}, {"name": "5x filament::components.modal.index", "param_count": null, "params": [], "start": **********.220684, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.228128, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament.resources.xdsl-service-resource.pages.filters-header", "param_count": null, "params": [], "start": **********.302572, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/filters-header.blade.phpfilament.resources.xdsl-service-resource.pages.filters-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Ffilters-header.blade.php&line=1", "ajax": false, "filename": "filters-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.resources.xdsl-service-resource.pages.filters-header"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.304645, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.32725, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.327943, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.35121, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x __components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.359197, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e93d402ed1f3da8a07f4840136a03cb"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.360551, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.361561, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.364903, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.365733, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.3683, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.36931, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.374534, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "3x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.375371, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.383995, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.sidebar-logo", "param_count": null, "params": [], "start": **********.404193, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar-logo.blade.phpfilament-panels::components.sidebar-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar-logo.blade.php&line=1", "ajax": false, "filename": "sidebar-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar-logo"}, {"name": "3x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.407563, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.sidebar.group"}, {"name": "6x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.408475, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.420897, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.42159, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.422557, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.422867, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.426804, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}]}, "queries": {"count": 53, "nb_statements": 53, "nb_visible_statements": 53, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.4308399999999997, "accumulated_duration_str": "1.43s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz'", "type": "query", "params": [], "bindings": ["L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.376213, "duration": 0.02558, "duration_str": "25.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 1.788}, {"sql": "select top 1 * from [users] where [id] = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.408177, "duration": 0.02388, "duration_str": "23.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 1.788, "width_percent": 1.669}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (14) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.439074, "duration": 0.02075, "duration_str": "20.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 3.457, "width_percent": 1.45}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:14')", "type": "query", "params": [], "bindings": ["filament-excel:exports:14"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.467718, "duration": 0.019870000000000002, "duration_str": "19.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 4.907, "width_percent": 1.389}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:14', 'illuminate:cache:flexible:created:filament-excel:exports:14')", "type": "query", "params": [], "bindings": ["filament-excel:exports:14", "illuminate:cache:flexible:created:filament-excel:exports:14"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.4891498, "duration": 0.019379999999999998, "duration_str": "19.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 6.296, "width_percent": 1.354}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 14 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [14, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.513001, "duration": 0.01813, "duration_str": "18.13ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 7.65, "width_percent": 1.267}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.536874, "duration": 0.02414, "duration_str": "24.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 8.917, "width_percent": 1.687}, {"sql": "select * from [cache] where [key] in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.5621421, "duration": 0.021, "duration_str": "21ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 10.604, "width_percent": 1.468}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.5843081, "duration": 0.016640000000000002, "duration_str": "16.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 12.072, "width_percent": 1.163}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.603665, "duration": 0.0165, "duration_str": "16.5ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 13.235, "width_percent": 1.153}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.62109, "duration": 0.01844, "duration_str": "18.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 14.388, "width_percent": 1.289}, {"sql": "select * from [cache] where [key] in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.6603532, "duration": 0.05346, "duration_str": "53.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 15.677, "width_percent": 3.736}, {"sql": "select [permissions].*, [model_has_permissions].[model_id] as [pivot_model_id], [model_has_permissions].[permission_id] as [pivot_permission_id], [model_has_permissions].[model_type] as [pivot_model_type] from [permissions] inner join [model_has_permissions] on [permissions].[id] = [model_has_permissions].[permission_id] where [model_has_permissions].[model_id] in (14) and [model_has_permissions].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.725335, "duration": 0.02268, "duration_str": "22.68ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "nourtel", "explain": null, "start_percent": 19.413, "width_percent": 1.585}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (14) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.7496731, "duration": 0.01662, "duration_str": "16.62ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "nourtel", "explain": null, "start_percent": 20.998, "width_percent": 1.162}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl'", "type": "query", "params": [], "bindings": ["xdsl"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8192792, "duration": 0.14354, "duration_str": "144ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "nourtel", "explain": null, "start_percent": 22.16, "width_percent": 10.032}, {"sql": "select top 10 * from [mesures] where [mesure_type] = 'xdsl' order by [mesure_date] desc", "type": "query", "params": [], "bindings": ["xdsl"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.9657052, "duration": 0.25838, "duration_str": "258ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 32.192, "width_percent": 18.058}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.31637, "duration": 0.02289, "duration_str": "22.89ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 50.25, "width_percent": 1.6}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.340012, "duration": 0.02951, "duration_str": "29.51ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 51.849, "width_percent": 2.062}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3718739, "duration": 0.027620000000000002, "duration_str": "27.62ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 53.912, "width_percent": 1.93}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.40109, "duration": 0.02761, "duration_str": "27.61ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 55.842, "width_percent": 1.93}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.451843, "duration": 0.01755, "duration_str": "17.55ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 57.772, "width_percent": 1.227}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.470285, "duration": 0.01809, "duration_str": "18.09ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 58.998, "width_percent": 1.264}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.489018, "duration": 0.01729, "duration_str": "17.29ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 60.263, "width_percent": 1.208}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5079608, "duration": 0.0185, "duration_str": "18.5ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 61.471, "width_percent": 1.293}, {"sql": "select top 1 * from [users] where [users].[id] = '25'", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.555506, "duration": 0.018179999999999998, "duration_str": "18.18ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 62.764, "width_percent": 1.271}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (25) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.574712, "duration": 0.01943, "duration_str": "19.43ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 64.034, "width_percent": 1.358}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '8' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.595087, "duration": 0.019489999999999997, "duration_str": "19.49ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 65.392, "width_percent": 1.362}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.615627, "duration": 0.019399999999999997, "duration_str": "19.4ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 66.754, "width_percent": 1.356}, {"sql": "select top 1 * from [users] where [users].[id] = '27'", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.670677, "duration": 0.018510000000000002, "duration_str": "18.51ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 68.11, "width_percent": 1.294}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (27) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.689868, "duration": 0.01744, "duration_str": "17.44ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 69.404, "width_percent": 1.219}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '9' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.709553, "duration": 0.01884, "duration_str": "18.84ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 70.623, "width_percent": 1.317}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7308452, "duration": 0.02309, "duration_str": "23.09ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 71.94, "width_percent": 1.614}, {"sql": "select top 1 * from [users] where [users].[id] = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.792124, "duration": 0.01858, "duration_str": "18.58ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 73.553, "width_percent": 1.299}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (28) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.812107, "duration": 0.0189, "duration_str": "18.9ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 74.852, "width_percent": 1.321}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '9' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.831686, "duration": 0.01807, "duration_str": "18.07ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 76.173, "width_percent": 1.263}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8503911, "duration": 0.0173, "duration_str": "17.3ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 77.436, "width_percent": 1.209}, {"sql": "select top 1 * from [users] where [users].[id] = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8929179, "duration": 0.01799, "duration_str": "17.99ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 78.645, "width_percent": 1.257}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (28) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.911679, "duration": 0.01787, "duration_str": "17.87ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 79.902, "width_percent": 1.249}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '9' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9302828, "duration": 0.01799, "duration_str": "17.99ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 81.151, "width_percent": 1.257}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9489908, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 82.408, "width_percent": 1.207}, {"sql": "select top 1 * from [users] where [users].[id] = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.996204, "duration": 0.01773, "duration_str": "17.73ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 83.615, "width_percent": 1.239}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (28) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.01458, "duration": 0.01766, "duration_str": "17.66ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 84.854, "width_percent": 1.234}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '9' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.032934, "duration": 0.01692, "duration_str": "16.92ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 86.089, "width_percent": 1.183}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0505621, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 87.271, "width_percent": 1.347}, {"sql": "select top 1 * from [users] where [users].[id] = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.100234, "duration": 0.01739, "duration_str": "17.39ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 88.618, "width_percent": 1.215}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (28) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.118322, "duration": 0.016050000000000002, "duration_str": "16.05ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 89.833, "width_percent": 1.122}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '9' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.135051, "duration": 0.01853, "duration_str": "18.53ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 90.955, "width_percent": 1.295}, {"sql": "select top 1 * from [regions] where [regions].[id] = '14' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.154511, "duration": 0.01777, "duration_str": "17.77ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 92.25, "width_percent": 1.242}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 14 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [14, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, {"index": 15, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.229769, "duration": 0.01654, "duration_str": "16.54ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:57", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=57", "ajax": false, "filename": "ListXDSLServices.php", "line": "57"}, "connection": "nourtel", "explain": null, "start_percent": 93.492, "width_percent": 1.156}, {"sql": "select [equipe_id] from [equipe_agent] where [user_id] = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 81}, {"index": 14, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.248689, "duration": 0.01648, "duration_str": "16.48ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:81", "source": {"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=81", "ajax": false, "filename": "ListXDSLServices.php", "line": "81"}, "connection": "nourtel", "explain": null, "start_percent": 94.648, "width_percent": 1.152}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3')", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.265797, "duration": 0.01775, "duration_str": "17.75ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:97", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=97", "ajax": false, "filename": "ListXDSLServices.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 95.8, "width_percent": 1.241}, {"sql": "select count(distinct [num_ligne]) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3') and [num_ligne] is not null", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.284361, "duration": 0.01691, "duration_str": "16.91ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:103", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=103", "ajax": false, "filename": "ListXDSLServices.php", "line": "103"}, "connection": "nourtel", "explain": null, "start_percent": 97.04, "width_percent": 1.182}, {"sql": "update [sessions] set [payload] = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUGFXeG9QTHIzeEF6R1ZScUM5czJ1bUFJNzQ1SEU2czYyOXRRMmZoMCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwczovL25vdXJ0ZWwudGVzdC94LWQtcy1sLXNlcnZpY2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRiaTdXcndPcDBGWXlSY0txMGpMOUp1QWpSWTFjeTdXUmNtWGJsSUh6RVB4TDZoLjZLZVpaVyI7fQ==', [last_activity] = **********, [user_id] = 14, [ip_address] = '127.0.0.1', [user_agent] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where [id] = 'L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUGFXeG9QTHIzeEF6R1ZScUM5czJ1bUFJNzQ1SEU2czYyOXRRMmZoMCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwczovL25vdXJ0ZWwudGVzdC94LWQtcy1sLXNlcnZpY2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRiaTdXcndPcDBGWXlSY0txMGpMOUp1QWpSWTFjeTdXUmNtWGJsSUh6RVB4TDZoLjZLZVpaVyI7fQ==", **********, 14, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.431194, "duration": 0.02544, "duration_str": "25.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "nourtel", "explain": null, "start_percent": 98.222, "width_percent": 1.778}]}, "models": {"data": {"App\\Models\\Mesure": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}, "App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Equipe": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FEquipe.php&line=1", "ajax": false, "filename": "Equipe.php", "line": "?"}}, "App\\Models\\Region": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FRegion.php&line=1", "ajax": false, "filename": "Region.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 36, "is_counter": true}, "livewire": {"data": {"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services #jW8LzfTpfA604IZDKgM7": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"date_range\" => array:2 [\n        \"created_from\" => null\n        \"created_until\" => null\n      ]\n      \"num_ligne_filter\" => array:1 [\n        \"num_ligne\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services\"\n  \"component\" => \"App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices\"\n  \"id\" => \"jW8LzfTpfA604IZDKgM7\"\n]", "filament.livewire.notifications #Mp3UkZRy8Qhi0BtZZawl": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2666\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"Mp3UkZRy8Qhi0BtZZawl\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 58, "messages": [{"message": "[\n  ability => reorder_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1654664163 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">reorder_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654664163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.772259, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1786023127 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786023127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.772886, "xdebug_link": null}, {"message": "[\n  ability => delete_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-234285774 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">delete_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234285774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.796505, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1279578296 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279578296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.796626, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-150879476 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150879476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.266748, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=244),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1738603587 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=244)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=244)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738603587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.266867, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1772757455 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772757455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.290704, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=241),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-166363708 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=241)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=241)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166363708\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.290829, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1315388348 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315388348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.432569, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=242),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-263638355 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=242)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=242)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263638355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.4327, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1022539562 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022539562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.529541, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=240),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-815167978 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=240)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=240)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815167978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.529659, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-663347445 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663347445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.639966, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=239),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1897254704 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=239)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=239)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897254704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.640098, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1802074390 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802074390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.758832, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=235),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1712256676 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=235)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=235)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712256676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.758955, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1740765369 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740765369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.870599, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=236),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1412773258 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=236)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=236)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412773258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.870738, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1086768834 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086768834\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.970539, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=237),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1558081727 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=237)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=237)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558081727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.970678, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-734424878 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734424878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.07516, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=238),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-125917988 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=238)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=238)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125917988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.07538, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-121565438 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121565438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.175749, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=233),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-319836413 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=233)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=233)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319836413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.175879, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2043519252 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043519252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.330113, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1379318756 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379318756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.333664, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1085026889 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085026889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.333904, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-428870239 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428870239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.336224, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-296922208 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296922208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.336405, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2041485248 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041485248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.338711, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-146948959 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146948959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.338916, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1491825803 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491825803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.340558, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1193382199 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193382199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.340745, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-472925388 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472925388\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.342628, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-589216291 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589216291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.342806, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176089357 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176089357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.344975, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 14,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1878403714 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878403714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.345123, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1904284856 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904284856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.346506, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1103732026 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103732026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.346671, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-559722786 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559722786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.349826, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 14,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1564907989 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564907989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.349953, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-331730514 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331730514\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.387909, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-458648403 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-458648403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.391337, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1691841622 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691841622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.391554, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-808131359 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808131359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.39616, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-294006564 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294006564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.396383, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993694269 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993694269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.398292, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2137597960 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137597960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.398484, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-34980998 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34980998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.400499, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-531804290 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531804290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.400669, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1150659289 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150659289\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.401577, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-243958677 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243958677\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.401689, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793371902 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793371902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.40227, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 14,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-627628023 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627628023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.402367, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273389590 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273389590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.402963, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1387290948 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387290948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.403056, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-887271794 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887271794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.403784, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 14,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932582790 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932582790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.403877, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices", "uri": "GET x-d-s-l-services", "controller": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices@render<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/x-d-s-l-services", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor", "duration": "2.99s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ilo4ODNkZVQ0OEp0aXJSdDBhR2VSZEE9PSIsInZhbHVlIjoiVTMwaHUyQTYvYVo2S2xhSlh4KzVnalFPdk8wcHlOQ094cUhYRE4rQ1ZVS1JNVFJyL1FaK2dTZFg1QldVdkkxZ0xON1ZNV2c3UDVOWGc4MmN6YVVsVjVQOFVNQnV1L0VpYXUyTW05aG13OTNocTM1VlhTcnhnTVpXVzBQQWp3blE5elJ2VmlmSUlncDFiWFdBVGNsMEIzVzIvMnRHQjRZcTJkUkpPejJiQVRBSzd6NW5oUlF1NEdjWXF6aU5wSnJHdE8vbll0UHVOWWZ5VVYrOWsydjh1NHNyd0w0UURJdFdMSDduTmhML0orYz0iLCJtYWMiOiI2YWNjMjQ4ZGNmMDRmNjA2ZjQ2YTY2NzkwY2VjYjcyOGE5NWM0ODQ4ZTI4MDJlOWE0ZjQwY2YwMzU5NmY1MjM4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBLM2lteHJMaDdaUzJRdTA4ZUZXa3c9PSIsInZhbHVlIjoiL0cvUVRCNnlPUXh0b3R3RkR3cHE5U2tPSTdTMEFvYnhySHRxMnRwdURMWmtjSXUvM3RZUE8zdi9tQWpBMFlwYlRvRWlFNm5YYTZGaWFtT1VvT2QyMlA4RFZ5c2RqREZJSWVHZVdyZVdFQmdOVXNMVUVnWVFna3I2Nyt3TzM4NjUiLCJtYWMiOiIzODM1YjY4MGEwOTM0Njg5M2JkYmNlM2YyZjJjZmJmZmQyMzg2MTkyODEwNGExZTNiNWFkZmE3NTc4NmMyNGVjIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6IlRibC9URklxTjhrclJhSlZTME9wUFE9PSIsInZhbHVlIjoibDF5WGpYU1JpM2sxNzkwUmw2N3dobDJXVk1WZXl2c2VTSk1DKzdSanhFMldFK3c4a09JSVVRRm9UNUlOTTNhNVJvUHQyN0tnTXRTWVN0Sjk0ZzRna2RKaHB1K0cwQzBGdStyWkVXWis0RXNORVFUM01QcVJqdDdDTzBzZFdONk0iLCJtYWMiOiI1ZWQxZTc3NWM5YWM3OGQ2YzNlNDU5OTQ4MDk1MTliYTAyODk3M2I5NjY0YzRlNDA1NmExMTRhOTc0OGZjYjM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://nourtel.test/?equipe_id=3&amp;date=2025-06-01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-427776508 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">14|yprycTiBDAKXrGkU9iUg8TnuMhdvQFVZtiTQDsiiQrLDFuhuuN4ha1TwoWdX|$2y$12$bi7WrwOp0FYyRcKq0jL9JuAjRY1cy7WRcmXblIHzEPxL6h.6KeZZW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PaWxoPLr3xAzGVRqC9s2umAI745HE6s629tQ2fh0</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427776508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-306255774 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:20:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImdiSHdNeUVGY1lMNDQ3WDdSYnBwZWc9PSIsInZhbHVlIjoiVktINmVEZzEzSUJhL2NSeWtUZ05wcmE5SUl1RFdITm9kNEw3Q0YvU2cvdkRqMTNyZDcxTGxjaTRLQlBBM2JLV3JWUzNKLy9rY0JpMHVNM29nZGhJOGJIc3dxOTNzWGNGdjZtSGZBRDVXczJ5RS95VXVsTHFUUm1yUWFzbWVnV1QiLCJtYWMiOiI3M2QwZjNkMmRjYWVkYzk2ZWY4MzY4ZDY4ZGJjMThmZjNlMmU4MDU1Y2M5OGFmMTZlMmRjZmRkYzE0Yjc3ZDk1IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:20:26 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"457 characters\">line_analyzer_session=eyJpdiI6IjhaWGpKeWlZTTVLYlZubkxqd1Bmbmc9PSIsInZhbHVlIjoiQjFCcTdrL0NmOVRuOUwvM3hGY1hDTW9lNWxKM2E5R0phTWI3ZUtaanJpam5tYzJUZU9UTjdaOTFCelgwVG4zdEFWQ2tHWnk3azFBd1pybGRpK3BEYU84cWZ3VGFkQjV3Y1FxRTh3SFR6L25KT2t0c3VOQ2hMU1hpSlRsNHpIRmIiLCJtYWMiOiI4MmRiOGExMWFiNGMzYWVlNjllMWYzNTlkOWVlODI0N2ZlZGQ2MGJjMjE0ZjNlOWJhNTczOWQwMDE2NWM0NjEyIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:20:26 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImdiSHdNeUVGY1lMNDQ3WDdSYnBwZWc9PSIsInZhbHVlIjoiVktINmVEZzEzSUJhL2NSeWtUZ05wcmE5SUl1RFdITm9kNEw3Q0YvU2cvdkRqMTNyZDcxTGxjaTRLQlBBM2JLV3JWUzNKLy9rY0JpMHVNM29nZGhJOGJIc3dxOTNzWGNGdjZtSGZBRDVXczJ5RS95VXVsTHFUUm1yUWFzbWVnV1QiLCJtYWMiOiI3M2QwZjNkMmRjYWVkYzk2ZWY4MzY4ZDY4ZGJjMThmZjNlMmU4MDU1Y2M5OGFmMTZlMmRjZmRkYzE0Yjc3ZDk1IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:20:26 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"429 characters\">line_analyzer_session=eyJpdiI6IjhaWGpKeWlZTTVLYlZubkxqd1Bmbmc9PSIsInZhbHVlIjoiQjFCcTdrL0NmOVRuOUwvM3hGY1hDTW9lNWxKM2E5R0phTWI3ZUtaanJpam5tYzJUZU9UTjdaOTFCelgwVG4zdEFWQ2tHWnk3azFBd1pybGRpK3BEYU84cWZ3VGFkQjV3Y1FxRTh3SFR6L25KT2t0c3VOQ2hMU1hpSlRsNHpIRmIiLCJtYWMiOiI4MmRiOGExMWFiNGMzYWVlNjllMWYzNTlkOWVlODI0N2ZlZGQ2MGJjMjE0ZjNlOWJhNTczOWQwMDE2NWM0NjEyIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:20:26 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306255774\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-969302088 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PaWxoPLr3xAzGVRqC9s2umAI745HE6s629tQ2fh0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://nourtel.test/x-d-s-l-services</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$bi7WrwOp0FYyRcKq0jL9JuAjRY1cy7WRcmXblIHzEPxL6h.6KeZZW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969302088\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices"}, "badge": null}}