import fs from 'fs';
import path from 'path';

// Create the destination directory if it doesn't exist
const destDir = path.resolve('./public/images');
if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
}

// Copy files from resources/assets/images to public/images
const sourceDir = path.resolve('./resources/assets/images');
if (fs.existsSync(sourceDir)) {
    const files = fs.readdirSync(sourceDir);
    files.forEach(file => {
        const sourcePath = path.join(sourceDir, file);
        const destPath = path.join(destDir, file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`Copied ${sourcePath} to ${destPath}`);
    });
}
