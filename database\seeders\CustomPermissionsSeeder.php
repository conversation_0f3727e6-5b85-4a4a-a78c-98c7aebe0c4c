<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CustomPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define custom permissions
        $customPermissions = [
            'view_service_gpon' => 'View Service GPON',
            'view_consultation_ligne' => 'View Consultation Ligne',
            'view_historique_qualification' => 'View Historique Qualification',
        ];

        // Create permissions in the database
        foreach ($customPermissions as $permission => $description) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Assign all custom permissions to super_admin role
        $superAdminRole = Role::where('name', 'super_admin')->first();
        if ($superAdminRole) {
            $permissions = Permission::whereIn('name', array_keys($customPermissions))->get();
            $superAdminRole->givePermissionTo($permissions);
        }

        $this->command->info('Custom permissions created and assigned to super_admin role.');
    }
}
