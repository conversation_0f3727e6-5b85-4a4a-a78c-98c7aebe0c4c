{"__meta": {"id": "01JWTMGHBZ2MTPJ4VWV8JRV6RB", "datetime": "2025-06-03 10:13:38", "utime": **********.3042, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748945616.620823, "end": **********.304212, "duration": 1.6833891868591309, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1748945616.620823, "relative_start": 0, "end": **********.256582, "relative_end": **********.256582, "duration": 0.****************, "duration_str": "636ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.256593, "relative_start": 0.****************, "end": **********.304214, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.40885, "relative_start": 0.****************, "end": **********.41069, "relative_end": **********.41069, "duration": 0.0018401145935058594, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::resources.pages.list-records", "start": **********.701973, "relative_start": 1.****************, "end": **********.701973, "relative_end": **********.701973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.resources.tabs", "start": **********.703179, "relative_start": 1.****************, "end": **********.703179, "relative_end": **********.703179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.870418, "relative_start": 1.2495951652526855, "end": **********.870418, "relative_end": **********.870418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.870817, "relative_start": 1.2499940395355225, "end": **********.870817, "relative_end": **********.870817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.872083, "relative_start": 1.2512600421905518, "end": **********.872083, "relative_end": **********.872083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.872914, "relative_start": 1.2520911693572998, "end": **********.872914, "relative_end": **********.872914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.873832, "relative_start": 1.2530090808868408, "end": **********.873832, "relative_end": **********.873832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.87552, "relative_start": 1.2546970844268799, "end": **********.87552, "relative_end": **********.87552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.875959, "relative_start": 1.2551360130310059, "end": **********.875959, "relative_end": **********.875959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.876759, "relative_start": 1.2559361457824707, "end": **********.876759, "relative_end": **********.876759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.877229, "relative_start": 1.2564060688018799, "end": **********.877229, "relative_end": **********.877229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.877487, "relative_start": 1.2566640377044678, "end": **********.877487, "relative_end": **********.877487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.877673, "relative_start": 1.256850004196167, "end": **********.877673, "relative_end": **********.877673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2c7fb83fee8872fcca12776962c3681c", "start": **********.879206, "relative_start": 1.25838303565979, "end": **********.879206, "relative_end": **********.879206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.879659, "relative_start": 1.258836030960083, "end": **********.879659, "relative_end": **********.879659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.880566, "relative_start": 1.2597429752349854, "end": **********.880566, "relative_end": **********.880566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.881376, "relative_start": 1.2605531215667725, "end": **********.881376, "relative_end": **********.881376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.881672, "relative_start": 1.2608489990234375, "end": **********.881672, "relative_end": **********.881672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.882364, "relative_start": 1.2615411281585693, "end": **********.882364, "relative_end": **********.882364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.882802, "relative_start": 1.261979103088379, "end": **********.882802, "relative_end": **********.882802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.884533, "relative_start": 1.2637100219726562, "end": **********.884533, "relative_end": **********.884533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.884893, "relative_start": 1.2640700340270996, "end": **********.884893, "relative_end": **********.884893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.885703, "relative_start": 1.2648801803588867, "end": **********.885703, "relative_end": **********.885703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": **********.886195, "relative_start": 1.2653720378875732, "end": **********.886195, "relative_end": **********.886195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.887823, "relative_start": 1.2670001983642578, "end": **********.887823, "relative_end": **********.887823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.889098, "relative_start": 1.2682750225067139, "end": **********.889098, "relative_end": **********.889098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.889384, "relative_start": 1.2685611248016357, "end": **********.889384, "relative_end": **********.889384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.91477, "relative_start": 1.2939469814300537, "end": **********.91477, "relative_end": **********.91477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.915813, "relative_start": 1.294990062713623, "end": **********.915813, "relative_end": **********.915813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.917127, "relative_start": 1.2963039875030518, "end": **********.917127, "relative_end": **********.917127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.917483, "relative_start": 1.2966601848602295, "end": **********.917483, "relative_end": **********.917483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.917861, "relative_start": 1.2970380783081055, "end": **********.917861, "relative_end": **********.917861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.919731, "relative_start": 1.298907995223999, "end": **********.919731, "relative_end": **********.919731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.919963, "relative_start": 1.2991399765014648, "end": **********.919963, "relative_end": **********.919963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.920735, "relative_start": 1.2999119758605957, "end": **********.920735, "relative_end": **********.920735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.922236, "relative_start": 1.3014130592346191, "end": **********.922236, "relative_end": **********.922236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.922569, "relative_start": 1.301746129989624, "end": **********.922569, "relative_end": **********.922569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.922935, "relative_start": 1.302112102508545, "end": **********.922935, "relative_end": **********.922935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.947227, "relative_start": 1.326404094696045, "end": **********.947227, "relative_end": **********.947227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.94802, "relative_start": 1.3271970748901367, "end": **********.94802, "relative_end": **********.94802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.949212, "relative_start": 1.3283891677856445, "end": **********.949212, "relative_end": **********.949212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.949518, "relative_start": 1.3286950588226318, "end": **********.949518, "relative_end": **********.949518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.949853, "relative_start": 1.3290300369262695, "end": **********.949853, "relative_end": **********.949853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.974538, "relative_start": 1.353715181350708, "end": **********.974538, "relative_end": **********.974538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.975296, "relative_start": 1.3544731140136719, "end": **********.975296, "relative_end": **********.975296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.976341, "relative_start": 1.355518102645874, "end": **********.976341, "relative_end": **********.976341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.976653, "relative_start": 1.355830192565918, "end": **********.976653, "relative_end": **********.976653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.976945, "relative_start": 1.3561220169067383, "end": **********.976945, "relative_end": **********.976945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.977222, "relative_start": 1.3563990592956543, "end": **********.977222, "relative_end": **********.977222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.977512, "relative_start": 1.3566889762878418, "end": **********.977512, "relative_end": **********.977512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.978487, "relative_start": 1.3576641082763672, "end": **********.978487, "relative_end": **********.978487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.979463, "relative_start": 1.358640193939209, "end": **********.979463, "relative_end": **********.979463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.980259, "relative_start": 1.35943603515625, "end": **********.980259, "relative_end": **********.980259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.981588, "relative_start": 1.360764980316162, "end": **********.981588, "relative_end": **********.981588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.983396, "relative_start": 1.3625731468200684, "end": **********.983396, "relative_end": **********.983396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.984243, "relative_start": 1.363420009613037, "end": **********.984243, "relative_end": **********.984243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.984874, "relative_start": 1.364051103591919, "end": **********.984874, "relative_end": **********.984874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.991562, "relative_start": 1.3707389831542969, "end": **********.991562, "relative_end": **********.991562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.00533, "relative_start": 1.384507179260254, "end": **********.00533, "relative_end": **********.00533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.006111, "relative_start": 1.3852880001068115, "end": **********.006111, "relative_end": **********.006111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.01338, "relative_start": 1.392557144165039, "end": **********.01338, "relative_end": **********.01338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.013844, "relative_start": 1.3930211067199707, "end": **********.013844, "relative_end": **********.013844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.014567, "relative_start": 1.3937439918518066, "end": **********.014567, "relative_end": **********.014567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.017151, "relative_start": 1.3963282108306885, "end": **********.017151, "relative_end": **********.017151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.017501, "relative_start": 1.3966782093048096, "end": **********.017501, "relative_end": **********.017501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.018389, "relative_start": 1.3975660800933838, "end": **********.018389, "relative_end": **********.018389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.01877, "relative_start": 1.397947072982788, "end": **********.01877, "relative_end": **********.01877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.024395, "relative_start": 1.4035720825195312, "end": **********.024395, "relative_end": **********.024395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.041687, "relative_start": 1.4208641052246094, "end": **********.041687, "relative_end": **********.041687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.042098, "relative_start": 1.4212751388549805, "end": **********.042098, "relative_end": **********.042098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.042869, "relative_start": 1.422046184539795, "end": **********.042869, "relative_end": **********.042869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.045575, "relative_start": 1.4247519969940186, "end": **********.045575, "relative_end": **********.045575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.046115, "relative_start": 1.4252920150756836, "end": **********.046115, "relative_end": **********.046115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.04698, "relative_start": 1.426156997680664, "end": **********.04698, "relative_end": **********.04698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.047311, "relative_start": 1.4264881610870361, "end": **********.047311, "relative_end": **********.047311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.052816, "relative_start": 1.431993007659912, "end": **********.052816, "relative_end": **********.052816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.070774, "relative_start": 1.449951171875, "end": **********.070774, "relative_end": **********.070774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.071162, "relative_start": 1.4503390789031982, "end": **********.071162, "relative_end": **********.071162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.071943, "relative_start": 1.451120138168335, "end": **********.071943, "relative_end": **********.071943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.074605, "relative_start": 1.453782081604004, "end": **********.074605, "relative_end": **********.074605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.074989, "relative_start": 1.4541661739349365, "end": **********.074989, "relative_end": **********.074989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.075827, "relative_start": 1.4550039768218994, "end": **********.075827, "relative_end": **********.075827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.076159, "relative_start": 1.455336093902588, "end": **********.076159, "relative_end": **********.076159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.081221, "relative_start": 1.4603981971740723, "end": **********.081221, "relative_end": **********.081221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.098096, "relative_start": 1.4772729873657227, "end": **********.098096, "relative_end": **********.098096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.098449, "relative_start": 1.477626085281372, "end": **********.098449, "relative_end": **********.098449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.099223, "relative_start": 1.4783999919891357, "end": **********.099223, "relative_end": **********.099223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.101731, "relative_start": 1.4809081554412842, "end": **********.101731, "relative_end": **********.101731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.102098, "relative_start": 1.4812750816345215, "end": **********.102098, "relative_end": **********.102098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.1029, "relative_start": 1.4820771217346191, "end": **********.1029, "relative_end": **********.1029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.103234, "relative_start": 1.4824111461639404, "end": **********.103234, "relative_end": **********.103234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.109631, "relative_start": 1.4888081550598145, "end": **********.109631, "relative_end": **********.109631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.127035, "relative_start": 1.5062119960784912, "end": **********.127035, "relative_end": **********.127035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.127435, "relative_start": 1.5066120624542236, "end": **********.127435, "relative_end": **********.127435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.128222, "relative_start": 1.507399082183838, "end": **********.128222, "relative_end": **********.128222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.130909, "relative_start": 1.5100860595703125, "end": **********.130909, "relative_end": **********.130909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.131325, "relative_start": 1.5105020999908447, "end": **********.131325, "relative_end": **********.131325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.132214, "relative_start": 1.5113911628723145, "end": **********.132214, "relative_end": **********.132214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.132556, "relative_start": 1.511733055114746, "end": **********.132556, "relative_end": **********.132556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.138091, "relative_start": 1.517268180847168, "end": **********.138091, "relative_end": **********.138091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.156181, "relative_start": 1.535358190536499, "end": **********.156181, "relative_end": **********.156181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.156597, "relative_start": 1.5357739925384521, "end": **********.156597, "relative_end": **********.156597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.157402, "relative_start": 1.5365791320800781, "end": **********.157402, "relative_end": **********.157402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.16002, "relative_start": 1.5391972064971924, "end": **********.16002, "relative_end": **********.16002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.160428, "relative_start": 1.5396051406860352, "end": **********.160428, "relative_end": **********.160428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.161322, "relative_start": 1.540499210357666, "end": **********.161322, "relative_end": **********.161322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.161691, "relative_start": 1.5408680438995361, "end": **********.161691, "relative_end": **********.161691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.167065, "relative_start": 1.5462419986724854, "end": **********.167065, "relative_end": **********.167065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.185676, "relative_start": 1.5648531913757324, "end": **********.185676, "relative_end": **********.185676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.186373, "relative_start": 1.5655500888824463, "end": **********.186373, "relative_end": **********.186373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.187551, "relative_start": 1.566728115081787, "end": **********.187551, "relative_end": **********.187551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.191776, "relative_start": 1.570953130722046, "end": **********.191776, "relative_end": **********.191776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.192381, "relative_start": 1.5715579986572266, "end": **********.192381, "relative_end": **********.192381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.193359, "relative_start": 1.5725359916687012, "end": **********.193359, "relative_end": **********.193359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.193751, "relative_start": 1.5729281902313232, "end": **********.193751, "relative_end": **********.193751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.199089, "relative_start": 1.5782661437988281, "end": **********.199089, "relative_end": **********.199089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.216129, "relative_start": 1.595306158065796, "end": **********.216129, "relative_end": **********.216129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.216492, "relative_start": 1.5956690311431885, "end": **********.216492, "relative_end": **********.216492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.217227, "relative_start": 1.5964040756225586, "end": **********.217227, "relative_end": **********.217227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.219687, "relative_start": 1.5988640785217285, "end": **********.219687, "relative_end": **********.219687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.220055, "relative_start": 1.5992321968078613, "end": **********.220055, "relative_end": **********.220055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.220917, "relative_start": 1.6000940799713135, "end": **********.220917, "relative_end": **********.220917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.221241, "relative_start": 1.6004180908203125, "end": **********.221241, "relative_end": **********.221241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.226286, "relative_start": 1.6054630279541016, "end": **********.226286, "relative_end": **********.226286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.243102, "relative_start": 1.622279167175293, "end": **********.243102, "relative_end": **********.243102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.24357, "relative_start": 1.6227471828460693, "end": **********.24357, "relative_end": **********.24357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.244366, "relative_start": 1.6235430240631104, "end": **********.244366, "relative_end": **********.244366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.2469, "relative_start": 1.6260771751403809, "end": **********.2469, "relative_end": **********.2469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.247276, "relative_start": 1.626453161239624, "end": **********.247276, "relative_end": **********.247276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.248092, "relative_start": 1.6272690296173096, "end": **********.248092, "relative_end": **********.248092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.248416, "relative_start": 1.6275930404663086, "end": **********.248416, "relative_end": **********.248416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": **********.254219, "relative_start": 1.6333961486816406, "end": **********.254219, "relative_end": **********.254219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.272798, "relative_start": 1.651975154876709, "end": **********.272798, "relative_end": **********.272798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.273226, "relative_start": 1.6524031162261963, "end": **********.273226, "relative_end": **********.273226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.273971, "relative_start": 1.6531481742858887, "end": **********.273971, "relative_end": **********.273971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.276408, "relative_start": 1.6555850505828857, "end": **********.276408, "relative_end": **********.276408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.276791, "relative_start": 1.655968189239502, "end": **********.276791, "relative_end": **********.276791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.277618, "relative_start": 1.6567950248718262, "end": **********.277618, "relative_end": **********.277618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.277949, "relative_start": 1.6571261882781982, "end": **********.277949, "relative_end": **********.277949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.279503, "relative_start": 1.6586802005767822, "end": **********.279503, "relative_end": **********.279503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.index", "start": **********.280167, "relative_start": 1.65934419631958, "end": **********.280167, "relative_end": **********.280167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.281332, "relative_start": 1.6605091094970703, "end": **********.281332, "relative_end": **********.281332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.282238, "relative_start": 1.6614151000976562, "end": **********.282238, "relative_end": **********.282238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.28266, "relative_start": 1.661837100982666, "end": **********.28266, "relative_end": **********.28266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.282936, "relative_start": 1.6621131896972656, "end": **********.282936, "relative_end": **********.282936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.283519, "relative_start": 1.662696123123169, "end": **********.283519, "relative_end": **********.283519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.283701, "relative_start": 1.6628780364990234, "end": **********.283701, "relative_end": **********.283701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.284198, "relative_start": 1.6633751392364502, "end": **********.284198, "relative_end": **********.284198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.285023, "relative_start": 1.6642000675201416, "end": **********.285023, "relative_end": **********.285023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.285265, "relative_start": 1.6644420623779297, "end": **********.285265, "relative_end": **********.285265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.28563, "relative_start": 1.6648070812225342, "end": **********.28563, "relative_end": **********.28563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.286089, "relative_start": 1.6652660369873047, "end": **********.286089, "relative_end": **********.286089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.28642, "relative_start": 1.6655972003936768, "end": **********.28642, "relative_end": **********.28642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.286744, "relative_start": 1.6659212112426758, "end": **********.286744, "relative_end": **********.286744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.287012, "relative_start": 1.666189193725586, "end": **********.287012, "relative_end": **********.287012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.287242, "relative_start": 1.6664190292358398, "end": **********.287242, "relative_end": **********.287242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.288899, "relative_start": 1.6680760383605957, "end": **********.288899, "relative_end": **********.288899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.289874, "relative_start": 1.669051170349121, "end": **********.289874, "relative_end": **********.289874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.290584, "relative_start": 1.6697611808776855, "end": **********.290584, "relative_end": **********.290584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.291198, "relative_start": 1.670375108718872, "end": **********.291198, "relative_end": **********.291198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.29187, "relative_start": 1.6710472106933594, "end": **********.29187, "relative_end": **********.29187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.292622, "relative_start": 1.6717991828918457, "end": **********.292622, "relative_end": **********.292622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.293767, "relative_start": 1.6729440689086914, "end": **********.293767, "relative_end": **********.293767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.breadcrumbs", "start": **********.294118, "relative_start": 1.673295021057129, "end": **********.294118, "relative_end": **********.294118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.294446, "relative_start": 1.6736230850219727, "end": **********.294446, "relative_end": **********.294446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.294688, "relative_start": 1.6738650798797607, "end": **********.294688, "relative_end": **********.294688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.294928, "relative_start": 1.674105167388916, "end": **********.294928, "relative_end": **********.294928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.296301, "relative_start": 1.6754779815673828, "end": **********.296301, "relative_end": **********.296301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.296644, "relative_start": 1.67582106590271, "end": **********.296644, "relative_end": **********.296644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.298114, "relative_start": 1.6772911548614502, "end": **********.298114, "relative_end": **********.298114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.298429, "relative_start": 1.6776061058044434, "end": **********.298429, "relative_end": **********.298429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.299229, "relative_start": 1.678406000137329, "end": **********.299229, "relative_end": **********.299229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.299668, "relative_start": 1.6788451671600342, "end": **********.299668, "relative_end": **********.299668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.299962, "relative_start": 1.6791391372680664, "end": **********.299962, "relative_end": **********.299962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.301476, "relative_start": 1.6806530952453613, "end": **********.302604, "relative_end": **********.302604, "duration": 0.0011279582977294922, "duration_str": "1.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 51874768, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 174, "nb_templates": 174, "templates": [{"name": "1x filament-panels::resources.pages.list-records", "param_count": null, "params": [], "start": **********.701956, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/resources/pages/list-records.blade.phpfilament-panels::resources.pages.list-records", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fresources%2Fpages%2Flist-records.blade.php&line=1", "ajax": false, "filename": "list-records.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::resources.pages.list-records"}, {"name": "1x filament-panels::components.resources.tabs", "param_count": null, "params": [], "start": **********.703168, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/resources/tabs.blade.phpfilament-panels::components.resources.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fresources%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.resources.tabs"}, {"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.870407, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "6x filament::components.button.index", "param_count": null, "params": [], "start": **********.870807, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament::components.button.index"}, {"name": "2x filament::components.icon-button", "param_count": null, "params": [], "start": **********.872072, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.icon-button"}, {"name": "33x filament::components.icon", "param_count": null, "params": [], "start": **********.872905, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 33, "name_original": "filament::components.icon"}, {"name": "1x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.875507, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.87595, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.list.item"}, {"name": "19x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.87722, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 19, "name_original": "filament::components.loading-indicator"}, {"name": "1x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.877478, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.list.index"}, {"name": "2x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.877665, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.index"}, {"name": "1x __components::2c7fb83fee8872fcca12776962c3681c", "param_count": null, "params": [], "start": **********.879181, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/2c7fb83fee8872fcca12776962c3681c.blade.php__components::2c7fb83fee8872fcca12776962c3681c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F2c7fb83fee8872fcca12776962c3681c.blade.php&line=1", "ajax": false, "filename": "2c7fb83fee8872fcca12776962c3681c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2c7fb83fee8872fcca12776962c3681c"}, {"name": "2x filament::components.input.index", "param_count": null, "params": [], "start": **********.881367, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.index"}, {"name": "7x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.881662, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 7, "name_original": "filament::components.input.wrapper"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.884523, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": **********.886185, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "25x filament::components.link", "param_count": null, "params": [], "start": **********.887807, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 25, "name_original": "filament::components.link"}, {"name": "3x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.915804, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "8x filament::components.grid.column", "param_count": null, "params": [], "start": **********.917117, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 8, "name_original": "filament::components.grid.column"}, {"name": "5x filament::components.grid.index", "param_count": null, "params": [], "start": **********.917475, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.grid.index"}, {"name": "1x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.920722, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "11x filament::components.input.checkbox", "param_count": null, "params": [], "start": **********.981575, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/checkbox.blade.phpfilament::components.input.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 11, "name_original": "filament::components.input.checkbox"}, {"name": "20x __components::********************************", "param_count": null, "params": [], "start": **********.013369, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/********************************.blade.php__components::********************************", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F********************************.blade.php&line=1", "ajax": false, "filename": "********************************.blade.php", "line": "?"}, "render_count": 20, "name_original": "__components::********************************"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.279491, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x filament::components.pagination.index", "param_count": null, "params": [], "start": **********.280158, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/index.blade.phpfilament::components.pagination.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.pagination.index"}, {"name": "2x filament::components.input.select", "param_count": null, "params": [], "start": **********.28265, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/select.blade.phpfilament::components.input.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.select"}, {"name": "5x filament::components.pagination.item", "param_count": null, "params": [], "start": **********.285256, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/item.blade.phpfilament::components.pagination.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.pagination.item"}, {"name": "5x filament::components.modal.index", "param_count": null, "params": [], "start": **********.288889, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.292612, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.293759, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.breadcrumbs", "param_count": null, "params": [], "start": **********.29411, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/breadcrumbs.blade.phpfilament::components.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.breadcrumbs"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.29492, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.296292, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.299953, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.41566, "accumulated_duration_str": "416ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC'", "type": "query", "params": [], "bindings": ["KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.414198, "duration": 0.01884, "duration_str": "18.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 4.533}, {"sql": "select top 1 * from [users] where [id] = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.439501, "duration": 0.01838, "duration_str": "18.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 4.533, "width_percent": 4.422}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4604971, "duration": 0.01841, "duration_str": "18.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 8.954, "width_percent": 4.429}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.481063, "duration": 0.018449999999999998, "duration_str": "18.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 13.384, "width_percent": 4.439}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.5005062, "duration": 0.019620000000000002, "duration_str": "19.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 17.822, "width_percent": 4.72}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 11 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [11, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.522063, "duration": 0.01864, "duration_str": "18.64ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 22.542, "width_percent": 4.484}, {"sql": "select * from [cache] where [key] in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.56316, "duration": 0.06398, "duration_str": "63.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 27.027, "width_percent": 15.392}, {"sql": "select [permissions].*, [model_has_permissions].[model_id] as [pivot_model_id], [model_has_permissions].[permission_id] as [pivot_permission_id], [model_has_permissions].[model_type] as [pivot_model_type] from [permissions] inner join [model_has_permissions] on [permissions].[id] = [model_has_permissions].[permission_id] where [model_has_permissions].[model_id] in (11) and [model_has_permissions].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.6344051, "duration": 0.019620000000000002, "duration_str": "19.62ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "nourtel", "explain": null, "start_percent": 42.419, "width_percent": 4.72}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (11) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.654895, "duration": 0.01889, "duration_str": "18.89ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "nourtel", "explain": null, "start_percent": 47.139, "width_percent": 4.545}, {"sql": "select count(*) as aggregate from [users]", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7114978, "duration": 0.019739999999999997, "duration_str": "19.74ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "nourtel", "explain": null, "start_percent": 51.684, "width_percent": 4.749}, {"sql": "select * from [users] order by [users].[id] asc offset 10 rows fetch next 10 rows only", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.732007, "duration": 0.02797, "duration_str": "27.97ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 56.433, "width_percent": 6.729}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11, 12, 14, 15, 16, 17, 18, 20, 21, 22) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.761114, "duration": 0.019850000000000003, "duration_str": "19.85ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 63.162, "width_percent": 4.776}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (11, 12, 14, 15, 16, 17, 18, 20, 21, 22) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 23, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.782064, "duration": 0.02004, "duration_str": "20.04ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 67.938, "width_percent": 4.821}, {"sql": "select [equipes].*, [equipe_agent].[user_id] as [pivot_user_id], [equipe_agent].[equipe_id] as [pivot_equipe_id] from [equipes] inner join [equipe_agent] on [equipes].[id] = [equipe_agent].[equipe_id] where [equipe_agent].[user_id] in (11, 12, 14, 15, 16, 17, 18, 20, 21, 22) and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 23, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8048139, "duration": 0.03606, "duration_str": "36.06ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 72.759, "width_percent": 8.675}, {"sql": "select [equipes].*, [equipe_responsable].[user_id] as [pivot_user_id], [equipe_responsable].[equipe_id] as [pivot_equipe_id] from [equipes] inner join [equipe_responsable] on [equipes].[id] = [equipe_responsable].[equipe_id] where [equipe_responsable].[user_id] in (11, 12, 14, 15, 16, 17, 18, 20, 21, 22) and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 23, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8430042, "duration": 0.01993, "duration_str": "19.93ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 81.434, "width_percent": 4.795}, {"sql": "select distinct [roles].* from [roles] left join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] order by [roles].[name] asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.895012, "duration": 0.018609999999999998, "duration_str": "18.61ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "nourtel", "explain": null, "start_percent": 86.229, "width_percent": 4.477}, {"sql": "select distinct [equipes].* from [equipes] left join [equipe_agent] on [equipes].[id] = [equipe_agent].[equipe_id] where [equipes].[deleted_at] is null order by [equipes].[nom] asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.927442, "duration": 0.01863, "duration_str": "18.63ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "nourtel", "explain": null, "start_percent": 90.706, "width_percent": 4.482}, {"sql": "select distinct [equipes].* from [equipes] left join [equipe_responsable] on [equipes].[id] = [equipe_responsable].[equipe_id] where [equipes].[deleted_at] is null order by [equipes].[nom] asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.953247, "duration": 0.02, "duration_str": "20ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "nourtel", "explain": null, "start_percent": 95.188, "width_percent": 4.812}]}, "models": {"data": {"App\\Models\\Equipe": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FEquipe.php&line=1", "ajax": false, "filename": "Equipe.php", "line": "?"}}, "App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 38, "is_counter": true}, "livewire": {"data": {"app.filament.resources.user-resource.pages.list-users #QdELz3i5FcMJFnjqNpWJ": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:4 [\n      \"roles\" => array:1 [\n        \"values\" => []\n      ]\n      \"matricule\" => array:1 [\n        \"matricule\" => null\n      ]\n      \"agents\" => array:1 [\n        \"values\" => []\n      ]\n      \"responsables\" => array:1 [\n        \"values\" => []\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 2\n    ]\n  ]\n  \"name\" => \"app.filament.resources.user-resource.pages.list-users\"\n  \"component\" => \"App\\Filament\\Resources\\UserResource\\Pages\\ListUsers\"\n  \"id\" => \"QdELz3i5FcMJFnjqNpWJ\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 124, "messages": [{"message": "[\n  ability => reorder_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2023063513 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">reorder_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023063513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.677018, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\User,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-598143455 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598143455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67751, "xdebug_link": null}, {"message": "[\n  ability => delete_any_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1428678076 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">delete_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428678076\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695376, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1263710118 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263710118\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.69548, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-880715831 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880715831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.987775, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1709190567 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709190567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.987903, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365077471 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365077471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.990358, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1854415530 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854415530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.990489, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-129572785 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129572785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008315, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1521266442 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521266442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008415, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426257017 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426257017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009852, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2117547843 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117547843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009945, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2026507970 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026507970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011948, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-595208775 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595208775\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012047, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-574234123 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574234123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.016869, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=11),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947136598 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947136598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.016969, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233456570 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233456570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021486, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1976611126 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976611126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021621, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-425575659 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425575659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023374, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1113905500 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113905500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023471, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273163942 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273163942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.037122, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-712236281 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712236281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.03723, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821977120 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821977120\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038605, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1377169715 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377169715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038714, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-863245938 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863245938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.040511, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1954053429 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954053429\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.040613, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1519849081 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519849081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.045215, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=12),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008468646 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=12)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=12)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008468646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.045333, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003286251 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003286251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.049597, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1794371050 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794371050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.049703, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-691503052 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691503052\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.051418, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1401305147 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401305147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.051515, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1918613053 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918613053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.066386, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-499521417 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499521417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.066491, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1873928588 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873928588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.067859, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-456182320 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456182320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.067954, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1539275294 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539275294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.069604, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1401861349 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401861349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.069707, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-923952862 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923952862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074287, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=14),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2073587634 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073587634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074389, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-856112478 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856112478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078359, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-312592030 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312592030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078465, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-510253304 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510253304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080179, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2055510848 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055510848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080284, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-922605694 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922605694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093651, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-764797023 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764797023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093751, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793340043 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793340043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095128, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-517362531 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517362531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095222, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1431809517 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431809517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096965, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-402113023 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402113023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097064, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1013000683 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013000683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101432, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=15),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1491077137 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491077137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101541, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1602388530 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602388530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106059, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-204151743 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204151743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106188, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177446722 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177446722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10834, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-485834248 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485834248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108531, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2041017201 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041017201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122449, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-477558080 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477558080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122549, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1537811551 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537811551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123913, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1922812340 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922812340\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.124024, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-111838854 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111838854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125863, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-935114305 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935114305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125974, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1665521100 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665521100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130528, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=16),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1089599646 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089599646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130649, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1414463930 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414463930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134885, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-136896712 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136896712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134995, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-532517364 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532517364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.136956, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1125402287 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125402287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137062, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-709064985 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709064985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.151604, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009724208 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009724208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.151726, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1392915291 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392915291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.153109, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-884427286 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884427286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.153204, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168279496 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168279496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.155024, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1896921606 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896921606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.155122, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2146733304 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146733304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159689, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=17),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1306862628 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=17)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=17)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306862628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159801, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-626331491 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626331491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.164188, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1214239124 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214239124\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.164297, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1499224737 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499224737\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.16599, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-831324257 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831324257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.166114, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-412805302 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412805302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.180247, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-550518223 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550518223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.180362, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1284049324 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284049324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181801, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1730509447 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730509447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181911, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-67526729 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67526729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183815, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-405495408 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405495408\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183927, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059387528 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059387528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.19108, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=18),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-852550280 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852550280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.191353, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-729919970 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729919970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196077, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1540077894 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540077894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196194, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1029016937 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029016937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197914, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-224599418 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224599418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.198023, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2072948473 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072948473\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211629, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1909092718 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909092718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21174, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1881494772 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881494772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.213188, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-375419852 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375419852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.213282, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-98967929 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98967929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215006, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1343879493 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343879493\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215112, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1423650013 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423650013\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219322, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=20),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-992306574 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992306574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219431, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-347051893 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347051893\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22338, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-238800537 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238800537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223478, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-764592189 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764592189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22528, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1194875060 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194875060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22538, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920658292 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920658292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.238309, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2132761478 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132761478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.238412, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1581965878 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581965878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.239966, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-408266142 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408266142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240067, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1217082316 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217082316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241909, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-21090096 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21090096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.242013, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1459617022 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459617022\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.246584, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=21),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-945886379 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=21)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=21)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945886379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.246686, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-356267119 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356267119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25104, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1659168504 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659168504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.251174, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1336117857 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336117857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.253077, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930056814 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930056814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25318, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532165032 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532165032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.267707, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-591210563 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591210563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.267858, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1234576605 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234576605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269239, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-999345180 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999345180\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269332, "xdebug_link": null}, {"message": "[\n  ability => update_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-129893162 data-indent-pad=\"  \"><span class=sf-dump-note>update_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129893162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271589, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1488969589 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488969589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271689, "xdebug_link": null}, {"message": "[\n  ability => delete_user,\n  target => null,\n  result => true,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-804001236 data-indent-pad=\"  \"><span class=sf-dump-note>delete_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804001236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276118, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\User(id=22),\n  result => true,\n  user => 11,\n  arguments => [0 => Object(App\\Models\\User)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1894835415 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\User(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\User(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\User)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894835415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276217, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\UserResource\\Pages\\ListUsers@gotoPage<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2FHandlesPagination.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2FHandlesPagination.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Features/SupportPagination/HandlesPagination.php:34-37</a>", "middleware": "web", "duration": "1.68s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-161252071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161252071\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-453845520 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1825 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;roles&quot;:[{&quot;values&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;matricule&quot;:[{&quot;matricule&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;agents&quot;:[{&quot;values&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;responsables&quot;:[{&quot;values&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;QdELz3i5FcMJFnjqNpWJ&quot;,&quot;name&quot;:&quot;app.filament.resources.user-resource.pages.list-users&quot;,&quot;path&quot;:&quot;users&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;fr&quot;},&quot;checksum&quot;:&quot;20cee4bd37af559d4ee76ad0e490d72ea4713c55397c682776cb5c7f18ea9217&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">gotoPage</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453845520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-226237113 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFrZlZkMlgwNU5TbVlCSjVEbTBnQVE9PSIsInZhbHVlIjoiQVFrUTM2N0dtUEhMYndaSThVMHhrQm1KeUZ0RVlXeTNRNWZGZG00ZDNjdWo3cUxNV2p3TURuYkZ1b1UrZ0kwTU9zUEhNc09XTWMrSytCMUV2dXNQaHpIOGZhb0l1UmM3NWUxajBVV3FsQ09aekZqMmdmUmpCZzZHS25pWWNFbG8iLCJtYWMiOiIyMTA0MTQyZWMxMzcxOTYzYTU3OTUxMTUzM2I5ZmY4Mjk0MGMxMDg2YTExYWFjYjc2NTk1MDg4MzY4N2U3Yjk3IiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6IjMzRCsvZ3RoT281OWlScFpsbVB3bEE9PSIsInZhbHVlIjoiNEcyMGR4Z2liYU5qZUQ4eTN1N0dObjc5Zy93UHFLeDhDeVJHdksxVERYV3RZckwrQURFMkQwS043SVBXaVpweGtCWmZLYkFoT3B5NG5aVCtYNUZrNjhMZlBoWitNMWg5Z0ZJVFRpWVQveFd4WFg0OUVHQ2w4c2NXTTlVNm9VVzgiLCJtYWMiOiIzNTI5ZDY2OGRhZjFiNGI5ZmNmOGQzMjBkY2U5NDU1NzY5NjE5OTgzYTMxZDQwZTAzMDc0YTE4ZjdhODVlMWM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://nourtel.test/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://nourtel.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2220</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226237113\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1294049212 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">11|fJAGQZo119SnWfhn9FJXssfvf2PrUlfpfx8jEF9wGJSlIASs9AiKyT8DxX1p|$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KBkWM6IupSMbksM75gzJN6L1E2k7ip8BEsNuWduC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294049212\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-435496112 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:13:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435496112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-501082447 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qkhlhoaez2qWWZbhcGDlTxKEInaBaMRL0dJGjNrD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://nourtel.test/users/2/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Imv6E6Jz/oDPQ7Co30JJE.s3mCfjIfcJfFKiqu5o73y6k1hktqJkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501082447\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}