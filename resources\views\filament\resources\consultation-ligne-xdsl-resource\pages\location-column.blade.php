@if($getRecord()->lat)
    <button
        type="button"
        onclick="showConsultationXDSLLocationMap({{ $getRecord()->id }}, {{ $getRecord()->lat }}, {{ $getRecord()->lng }}, '{{ $getRecord()->emplacement }}', '{{ $getRecord()->num_ligne }}', '{{ $getRecord()->xdsl_mode ?? '' }}', {{ $getRecord()->xdsl_up_stream ?? 'null' }}, {{ $getRecord()->xdsl_down_stream ?? 'null' }}, {{ $getRecord()->xdsl_max_up_attainable_rate ?? 'null' }}, {{ $getRecord()->xdsl_max_down_attainable_rate ?? 'null' }}, {{ $getRecord()->xdsl_snr_margin_up_stream ?? 'null' }}, {{ $getRecord()->xdsl_snr_margin_down_stream ?? 'null' }}, {{ $getRecord()->xdsl_attenuation_up_rate ?? 'null' }}, {{ $getRecord()->xdsl_attenuation_down_rate ?? 'null' }}, {{ $getRecord()->xdsl_crc_errors ?? 'null' }}, '{{ $getRecord()->creator->name ?? '' }}', '{{ $getRecord()->equipe->nom ?? '' }}', '{{ $getRecord()->region->nom ?? '' }}', '{{ $getRecord()->mesure_status }}', '{{ $getRecord()->mesure_date ? \Carbon\Carbon::parse($getRecord()->mesure_date)->format('d/m/Y H:i') : '' }}')"
        class="filament-button filament-button-size-sm bg-primary-600 hover:bg-primary-500 text-white inline-flex items-center justify-center py-1 px-3 rounded-lg font-medium gap-1 shadow focus:outline-none focus:ring-offset-2 focus:ring-2 focus:ring-primary-500"
    >
        <x-heroicon-o-map-pin class="w-4 h-4" />
        <span>Voir localisation</span>
    </button>
@endif
