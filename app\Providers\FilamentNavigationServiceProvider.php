<?php

namespace App\Providers;

use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Illuminate\Support\ServiceProvider;

class FilamentNavigationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Filament::serving(function () {
            // Ajouter des éléments de navigation personnalisés
            Filament::registerNavigationItems([
                // GPON Service Navigation Items
                NavigationItem::make('Historique Qualification')
                    ->url(route('filament.admin.resources.g-p-o-n-services.index'))
                    ->icon('heroicon-o-document-chart-bar')
                    ->group('Service GPON')
                    ->sort(2),
                NavigationItem::make('Consultation Ligne')
                    ->url(route('filament.admin.resources.consultation-ligne.index'))
                    ->icon('heroicon-o-document-magnifying-glass')
                    ->group('Service GPON')
                    ->sort(1),

                // xDSL Service Navigation Items
                NavigationItem::make('Historique Qualification')
                    ->url('/admin/resources/x-d-s-l-services')
                    ->icon('heroicon-o-document-chart-bar')
                    ->group('Service xDSL')
                    ->sort(2),
                NavigationItem::make('Consultation Ligne')
                    ->url('/admin/resources/consultation-ligne-xdsl')
                    ->icon('heroicon-o-document-magnifying-glass')
                    ->group('Service xDSL')
                    ->sort(1),
            ]);

            // Réorganiser les groupes de navigation
            Filament::registerNavigationGroups([
                NavigationGroup::make()
                    ->label('Tableau de bord'),
                NavigationGroup::make()
                    ->label('Service GPON'),
                NavigationGroup::make()
                    ->label('Service xDSL'),
                NavigationGroup::make()
                    ->label('Administration'),
            ]);
        });
    }
}
