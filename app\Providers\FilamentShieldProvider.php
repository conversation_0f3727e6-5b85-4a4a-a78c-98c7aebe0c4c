<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Spatie\Permission\Models\Permission;

class FilamentShieldProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register custom permissions for GPON pages
        $this->registerGPONPermissions();
    }

    /**
     * Register GPON-related permissions
     */
    protected function registerGPONPermissions(): void
    {
        // Define custom permissions
        $customPermissions = [
            // GPON permissions
            'view_service_gpon' => 'View Service GPON',
            'view_consultation_ligne' => 'View Consultation Ligne',
            'view_historique_qualification' => 'View Historique Qualification',

            // xDSL permissions
            'view_service_xdsl' => 'View Service xDSL',
            'view_consultation_ligne_xdsl' => 'View Consultation Ligne xDSL',
            'view_historique_qualification_xdsl' => 'View Historique Qualification xDSL',
        ];

        // Create permissions in the database
        foreach ($customPermissions as $permission => $description) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }
    }
}
