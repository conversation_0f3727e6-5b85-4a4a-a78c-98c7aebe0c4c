:root {
    --xdsl-gradient: linear-gradient(135deg, #10b981, #059669);
    --gpon-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --card-hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --card-border-radius: 1rem;
    --transition-speed: 0.3s;
}

.dashboard-stats-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
}

.dashboard-stat-card {
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    z-index: 1;
}

.dashboard-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #001e8c 0, #c832ff);
    opacity: 0;
    z-index: -1;
    transition: opacity var(--transition-speed);
    border-radius: var(--card-border-radius);
}

.dashboard-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-hover-shadow);
}

.dashboard-stat-card:hover::before {
    opacity: 1;
}

.dashboard-stat-card:hover .dashboard-stat-value,
.dashboard-stat-card:hover .dashboard-stat-label,
.dashboard-stat-card:hover .dashboard-stat-type {
    color: white;
}

.dashboard-stat-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    flex: 1;
}

.dashboard-stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-speed);
    margin-left: 1rem;
}

.dashboard-stat-card:hover .dashboard-stat-icon {
    transform: scale(1.1);
}

.dashboard-stat-icon.xdsl {
    background: var(--xdsl-gradient);
    color: white;
}

.dashboard-stat-icon.gpon {
    background: var(--gpon-gradient);
    color: white;
}

.dashboard-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    transition: color var(--transition-speed);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dashboard-stat-value {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    transition: color var(--transition-speed);
    margin-bottom: 0.25rem;
}

.dashboard-stat-type {
    font-size: 1rem;
    font-weight: 600;
    transition: color var(--transition-speed);
}

.dashboard-stat-type.xdsl {
    color: #10b981;
}

.dashboard-stat-type.gpon {
    color: #3b82f6;
}

/* Animation pour les valeurs */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-stat-value {
    animation: countUp 0.8s ease-out forwards;
}

@media (max-width: 1280px) {
    .dashboard-stats-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1024px) {
    .dashboard-stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 640px) {
    .dashboard-stats-container {
        grid-template-columns: 1fr;
    }
}
