<?php

namespace App\Filament\Pages;

use App\Models\User;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class Profile extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user';
    protected static ?string $navigationLabel = 'Profil';
    protected static ?string $title = 'Mon Profil';
    protected static ?string $slug = 'profile';
    protected static ?int $navigationSort = 100;

    protected static string $view = 'filament.pages.profile';

    public ?array $passwordData = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Changement Mot de passe')
                    ->schema([
                        TextInput::make('current_password')
                            ->label('Mot de passe actuel')
                            ->password()
                            ->required()
                            ->rule('current_password'),
                        TextInput::make('new_password')
                            ->label('Nouveau mot de passe')
                            ->password()
                            ->required()
                            ->rule(Password::min(8))
                            ->same('new_password_confirmation'),
                        TextInput::make('new_password_confirmation')
                            ->label('Confirmez le mot de passe')
                            ->password()
                            ->required(),
                    ])
            ])
            ->statePath('passwordData');
    }

    public function updatePassword()
    {
        $data = $this->form->getState();

        $this->validate([
            'passwordData.current_password' => ['required', 'current_password'],
            'passwordData.new_password' => ['required', 'min:8', 'same:passwordData.new_password_confirmation'],
            'passwordData.new_password_confirmation' => ['required'],
        ]);

        $user = User::find(Auth::id());
        $user->update([
            'password' => Hash::make($data['new_password']),
        ]);

        $this->passwordData = [];
        $this->form->fill();

        Notification::make()
            ->title('Mot de passe mis à jour avec succès')
            ->success()
            ->send();
    }

    public function getUser()
    {
        return Auth::user();
    }

    public function getUserEquipes()
    {
        $user = $this->getUser();

        // Récupérer les équipes dont l'utilisateur est membre
        $agentEquipes = DB::table('equipe_agent')
            ->join('equipes', 'equipe_agent.equipe_id', '=', 'equipes.id')
            ->where('equipe_agent.user_id', $user->id)
            ->select('equipes.nom')
            ->get();

        // Récupérer les équipes dont l'utilisateur est responsable
        $responsableEquipes = DB::table('equipe_responsable')
            ->join('equipes', 'equipe_responsable.equipe_id', '=', 'equipes.id')
            ->where('equipe_responsable.user_id', $user->id)
            ->select('equipes.nom')
            ->get();

        return [
            'agent' => $agentEquipes,
            'responsable' => $responsableEquipes,
        ];
    }

    public function getUserRegions()
    {
        $user = $this->getUser();

        // Récupérer les équipes de l'utilisateur
        $equipeIds = DB::table('equipe_agent')
            ->where('user_id', $user->id)
            ->pluck('equipe_id')
            ->toArray();

        $responsableEquipeIds = DB::table('equipe_responsable')
            ->where('user_id', $user->id)
            ->pluck('equipe_id')
            ->toArray();

        $allEquipeIds = array_unique(array_merge($equipeIds, $responsableEquipeIds));

        // Récupérer les régions associées à ces équipes
        $regions = DB::table('equipe_region')
            ->join('regions', 'equipe_region.region_id', '=', 'regions.id')
            ->whereIn('equipe_region.equipe_id', $allEquipeIds)
            ->select('regions.nom')
            ->distinct()
            ->get();

        return $regions;
    }

    public function getUserRole()
    {
        $user = $this->getUser();

        $role = DB::table('roles')
            ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('model_has_roles.model_type', get_class($user))
            ->select('roles.name')
            ->first();

        return $role ? $role->name : 'Aucun rôle';
    }
}
