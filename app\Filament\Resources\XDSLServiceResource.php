<?php

namespace App\Filament\Resources;

use App\Filament\Resources\XDSLServiceResource\Pages;
use App\Models\Mesure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class XDSLServiceResource extends Resource
{
    protected static ?string $model = Mesure::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Historique Qualification';
    protected static ?string $navigationGroup = 'Service xDSL';
    protected static ?int $navigationSort = 2;
    protected static ?int $navigationGroupSort = 2; // Pour s'assurer que le groupe apparaît après Service GPON

    public static function canAccess(): bool
    {
        // Autoriser l'accès à tous les utilisateurs authentifiés
        return Auth::check();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('created_by')
                    ->relationship('creator', 'name')
                    ->required()
                    ->disabled()
                    ->default(Auth::id()),

                Forms\Components\Select::make('equipe_id')
                    ->relationship('equipe', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->equipe_id),

                Forms\Components\Select::make('region_id')
                    ->relationship('region', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->region_id),

                Forms\Components\TextInput::make('num_ligne')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('emplacement')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('lat')
                    ->label('Latitude')
                    ->numeric()
                    ->required(),

                Forms\Components\TextInput::make('lng')
                    ->label('Longitude')
                    ->numeric()
                    ->required(),

                Forms\Components\Section::make('xDSL Measurements')
                    ->schema([
                        Forms\Components\TextInput::make('xdsl_mode')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('xdsl_channel_mode')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('xdsl_up_stream')
                            ->numeric()
                            ->suffix('kbps')
                            ->label('UP RATE (kbps)'),
                        Forms\Components\TextInput::make('xdsl_down_stream')
                            ->numeric()
                            ->suffix('kbps')
                            ->label('DW RATE (kbps)'),
                        Forms\Components\TextInput::make('xdsl_max_up_attainable_rate')
                            ->numeric()
                            ->suffix('kbps')
                            ->label('UP MAX RATE (kbps)'),
                        Forms\Components\TextInput::make('xdsl_max_down_attainable_rate')
                            ->numeric()
                            ->suffix('kbps')
                            ->label('DW MAX RATE (kbps)'),
                        Forms\Components\TextInput::make('xdsl_snr_margin_up_stream')
                            ->numeric()
                            ->suffix('db')
                            ->label('UP SNR (db)'),
                        Forms\Components\TextInput::make('xdsl_snr_margin_down_stream')
                            ->numeric()
                            ->suffix('db')
                            ->label('DW SNR (db)'),
                        Forms\Components\TextInput::make('xdsl_attenuation_up_rate')
                            ->numeric()
                            ->suffix('db')
                            ->label('UP ATTEN (db)'),
                        Forms\Components\TextInput::make('xdsl_attenuation_down_rate')
                            ->numeric()
                            ->suffix('db')
                            ->label('DW ATTEN (db)'),
                        Forms\Components\TextInput::make('xdsl_crc_errors')
                            ->numeric(),
                    ])->columns(2),

                Forms\Components\Section::make('Etat')
                    ->schema([
                        Forms\Components\Select::make('mesure_type')
                            ->options([
                                'gpon' => 'GPON',
                                'xdsl' => 'xDSL',
                                'both' => 'Both',
                            ])
                            ->required()
                            ->default('xdsl')
                            ->disabled(),
                        Forms\Components\Select::make('mesure_status')
                            ->options([
                                '1' => 'Positif',
                                '0' => 'Négatif',
                            ])
                            ->required()
                            ->default('1'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        // Récupérer l'utilisateur connecté
        $user = Auth::user();

        // Récupérer le rôle de l'utilisateur
        $userRole = DB::table('roles')
            ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('model_has_roles.model_type', get_class($user))
            ->select('roles.id')
            ->first();

        // Créer la requête de base
        $query = Mesure::query()->where('mesure_type', 'xdsl');

        // Appliquer les filtres basés sur le rôle
        // Si l'utilisateur est super_admin (id=1) ou admin (id=2), afficher toutes les mesures
        if ($userRole && ($userRole->id == 1 || $userRole->id == 2)) {
            // Ne pas ajouter de condition supplémentaire
        }
        // Si l'utilisateur est chef d'équipe (id=3), filtrer par ses équipes
        elseif ($userRole && $userRole->id == 3) {
            // Récupérer les équipes dont l'utilisateur est responsable
            $equipeIds = DB::table('equipe_responsable')
                ->where('user_id', $user->id)
                ->pluck('equipe_id')
                ->toArray();

            if (!empty($equipeIds)) {
                $query->whereIn('equipe_id', $equipeIds);
            } else {
                // Si l'utilisateur n'a pas d'équipes, ne rien afficher
                $query->whereRaw('1 = 0');
            }
        }
        // Pour les autres rôles (y compris id=4), ne montrer que les mesures créées par l'utilisateur
        else {
            $query->where('created_by', $user->id);
        }

        return $table
            ->query($query)
            ->defaultSort('mesure_date', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('num_ligne')
                    ->label('Num Ligne')
                    ->searchable(),
                Tables\Columns\TextColumn::make('emplacement')
                    ->label('Emplacement')
                    ->searchable(),
                Tables\Columns\TextColumn::make('xdsl_up_stream')
                    ->label('UP RATE')
                    ->numeric()
                    ->suffix(' kbps')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_down_stream')
                    ->label('DW RATE')
                    ->numeric()
                    ->suffix(' kbps')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_max_up_attainable_rate')
                    ->label('UP MAX RATE')
                    ->numeric()
                    ->suffix(' kbps')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_max_down_attainable_rate')
                    ->label('DW MAX RATE')
                    ->numeric()
                    ->suffix(' kbps')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_snr_margin_up_stream')
                    ->label('UP SNR')
                    ->numeric()
                    ->suffix(' db')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_snr_margin_down_stream')
                    ->label('DW SNR')
                    ->numeric()
                    ->suffix(' db')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_attenuation_up_rate')
                    ->label('UP ATTEN')
                    ->numeric()
                    ->suffix(' db')
                    ->sortable(),
                Tables\Columns\TextColumn::make('xdsl_attenuation_down_rate')
                    ->label('DW ATTEN')
                    ->numeric()
                    ->suffix(' db')
                    ->sortable(),
                Tables\Columns\ViewColumn::make('mesure_status')
                    ->label('Etat')
                    ->view('filament.resources.xdsl-service-resource.pages.status-column'),
                Tables\Columns\TextColumn::make('mesure_date')
                    ->label('Date')
                    ->dateTime('Y-m-d / H:i')
                    ->sortable(),
                Tables\Columns\ViewColumn::make('location')
                    ->label('Localisation')
                    ->view('filament.resources.xdsl-service-resource.pages.location-column'),
            ])
            ->filters([
                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Date de début'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Date de fin'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('mesure_date', '>=', $date),
                            )
                            ->when(
                                $data['created_until'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('mesure_date', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('num_ligne_filter')
                    ->form([
                        Forms\Components\TextInput::make('num_ligne')
                            ->label('Num Ligne')
                            ->placeholder('Recherche exacte par numéro'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['num_ligne'] ?? null,
                            fn (Builder $query, $numLigne): Builder => $query->where('num_ligne', '=', $numLigne),
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListXDSLServices::route('/'),
            'create' => Pages\CreateXDSLService::route('/create'),
            'edit' => Pages\EditXDSLService::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // La logique de filtrage est maintenant gérée dans la méthode table()
        // pour une meilleure cohérence avec les statistiques
        return parent::getEloquentQuery();
    }
}
