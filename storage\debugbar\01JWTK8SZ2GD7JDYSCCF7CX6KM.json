{"__meta": {"id": "01JWTK8SZ2GD7JDYSCCF7CX6KM", "datetime": "2025-06-03 09:51:56", "utime": **********.387176, "method": "GET", "uri": "/api/users/28/locations?equipe_id=9", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748944315.563519, "end": **********.387187, "duration": 0.8236680030822754, "duration_str": "824ms", "measures": [{"label": "Booting", "start": 1748944315.563519, "relative_start": 0, "end": **********.115694, "relative_end": **********.115694, "duration": 0.****************, "duration_str": "552ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.115712, "relative_start": 0.****************, "end": **********.387188, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.351595, "relative_start": 0.****************, "end": **********.355796, "relative_end": **********.355796, "duration": 0.004201173782348633, "duration_str": "4.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.385445, "relative_start": 0.****************, "end": **********.385743, "relative_end": **********.385743, "duration": 0.00029778480529785156, "duration_str": "298μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.385762, "relative_start": 0.****************, "end": **********.385775, "relative_end": **********.385775, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01968, "accumulated_duration_str": "19.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select [id], [num_ligne], [emplacement], [lat], [lng], [mesure_status], [created_at] from [mesures] where [created_by] = '28' and ([lat] != '0' or [lng] != '0') and [equipe_id] = '9' order by [id] asc", "type": "query", "params": [], "bindings": ["28", "0", "0", "9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.35774, "duration": 0.01968, "duration_str": "19.68ms", "memory": 0, "memory_str": null, "filename": "UserLocationController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=37", "ajax": false, "filename": "UserLocationController.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mesure": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/28/locations?equipe_id=9", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations", "uri": "GET api/users/{userId}/locations", "controller": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/UserLocationController.php:18-40</a>", "middleware": "api", "duration": "824ms", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1797203207 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>equipe_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797203207\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-731078533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-731078533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1876969955 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkpPeUVmMlk4ZDBGdmVoR3p1NEtKUkE9PSIsInZhbHVlIjoiWXlRcUVjMDlPN094T2Q5RmRPa2tobHBkOE1nMEMzeHVpcWdKUjQ1Z0FzL1Uvc05XeTBkSDkxUEM4V3BZUTlJYit0cTd3WmdvaE8rV3pxaWhJZXRxTCtHWTNSSkdvMC9vVTk0TUNJSTN3Ujl5TklOY0hVS2RNajFTL3U5Y3BVOGQiLCJtYWMiOiIzNWRjNTRkOTcyMmQyZjcwNjY3YmFlZThmNTcyZTIzZjJmMjUyNjI4ZGVhYjk2MjE3NjQwZWVlN2JjZDBhNjEyIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6IlRPTTdXQm0xYmhNVlFoQXkwOUwzR2c9PSIsInZhbHVlIjoiVnBCSHJrOHBCYjRCOGJDVlA3MEpkdnlUWHVGU2dqRm9BdENNRkRnM3JaZTMxSjRRL2E3b0ZUVVBhU1VVeHZwbG5MaDVXOU1pU21iR2taN1N0ajJMa1hHbG94VGJIdU13bnNIekRZOTRqdWQ3b1ZKVlQ0K3RjaXRQLzg2TWNRam0iLCJtYWMiOiI4MjM5NjkwZjk1NTkyNTdkMzg3NjI4ZGEzY2VhYWY4ZDAzOTJkMmI3OGEyOGRmNDBlMjZlNTM3M2RiZDcwZTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?region_id=&amp;equipe_id=9&amp;date=2025-05-25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876969955\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1107828433 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkpPeUVmMlk4ZDBGdmVoR3p1NEtKUkE9PSIsInZhbHVlIjoiWXlRcUVjMDlPN094T2Q5RmRPa2tobHBkOE1nMEMzeHVpcWdKUjQ1Z0FzL1Uvc05XeTBkSDkxUEM4V3BZUTlJYit0cTd3WmdvaE8rV3pxaWhJZXRxTCtHWTNSSkdvMC9vVTk0TUNJSTN3Ujl5TklOY0hVS2RNajFTL3U5Y3BVOGQiLCJtYWMiOiIzNWRjNTRkOTcyMmQyZjcwNjY3YmFlZThmNTcyZTIzZjJmMjUyNjI4ZGVhYjk2MjE3NjQwZWVlN2JjZDBhNjEyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlRPTTdXQm0xYmhNVlFoQXkwOUwzR2c9PSIsInZhbHVlIjoiVnBCSHJrOHBCYjRCOGJDVlA3MEpkdnlUWHVGU2dqRm9BdENNRkRnM3JaZTMxSjRRL2E3b0ZUVVBhU1VVeHZwbG5MaDVXOU1pU21iR2taN1N0ajJMa1hHbG94VGJIdU13bnNIekRZOTRqdWQ3b1ZKVlQ0K3RjaXRQLzg2TWNRam0iLCJtYWMiOiI4MjM5NjkwZjk1NTkyNTdkMzg3NjI4ZGEzY2VhYWY4ZDAzOTJkMmI3OGEyOGRmNDBlMjZlNTM3M2RiZDcwZTI2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107828433\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-901366364 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 09:51:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901366364\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1947818318 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1947818318\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/28/locations?equipe_id=9", "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations"}, "badge": null}}