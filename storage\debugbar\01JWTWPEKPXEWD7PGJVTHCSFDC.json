{"__meta": {"id": "01JWTWPEKPXEWD7PGJVTHCSFDC", "datetime": "2025-06-03 12:36:40", "utime": **********.694678, "method": "GET", "uri": "/x-d-s-l-services", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.420921, "end": **********.694691, "duration": 2.2737698554992676, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": **********.420921, "relative_start": 0, "end": **********.871266, "relative_end": **********.871266, "duration": 0.****************, "duration_str": "450ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.871283, "relative_start": 0.***************, "end": **********.694693, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.82s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.04018, "relative_start": 0.****************, "end": **********.042883, "relative_end": **********.042883, "duration": 0.002702951431274414, "duration_str": "2.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::resources.pages.list-records", "start": **********.476909, "relative_start": 1.**************, "end": **********.476909, "relative_end": **********.476909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.resources.tabs", "start": **********.478018, "relative_start": 1.****************, "end": **********.478018, "relative_end": **********.478018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.530985, "relative_start": 1.1100640296936035, "end": **********.530985, "relative_end": **********.530985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.531266, "relative_start": 1.1103448867797852, "end": **********.531266, "relative_end": **********.531266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.532334, "relative_start": 1.1114130020141602, "end": **********.532334, "relative_end": **********.532334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.533195, "relative_start": 1.112273931503296, "end": **********.533195, "relative_end": **********.533195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.535544, "relative_start": 1.1146228313446045, "end": **********.535544, "relative_end": **********.535544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.535977, "relative_start": 1.115055799484253, "end": **********.535977, "relative_end": **********.535977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.536944, "relative_start": 1.1160228252410889, "end": **********.536944, "relative_end": **********.536944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": **********.537334, "relative_start": 1.116412878036499, "end": **********.537334, "relative_end": **********.537334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.538569, "relative_start": 1.1176478862762451, "end": **********.538569, "relative_end": **********.538569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.539467, "relative_start": 1.1185460090637207, "end": **********.539467, "relative_end": **********.539467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.539677, "relative_start": 1.11875581741333, "end": **********.539677, "relative_end": **********.539677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.542457, "relative_start": 1.1215360164642334, "end": **********.542457, "relative_end": **********.542457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.542725, "relative_start": 1.1218039989471436, "end": **********.542725, "relative_end": **********.542725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.543508, "relative_start": 1.122586965560913, "end": **********.543508, "relative_end": **********.543508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.544867, "relative_start": 1.123945951461792, "end": **********.544867, "relative_end": **********.544867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.546263, "relative_start": 1.1253418922424316, "end": **********.546263, "relative_end": **********.546263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.546991, "relative_start": 1.1260700225830078, "end": **********.546991, "relative_end": **********.546991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.547877, "relative_start": 1.1269559860229492, "end": **********.547877, "relative_end": **********.547877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.54896, "relative_start": 1.1280388832092285, "end": **********.54896, "relative_end": **********.54896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.54925, "relative_start": 1.128328800201416, "end": **********.54925, "relative_end": **********.54925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.54964, "relative_start": 1.1287188529968262, "end": **********.54964, "relative_end": **********.54964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.551499, "relative_start": 1.130577802658081, "end": **********.551499, "relative_end": **********.551499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.551727, "relative_start": 1.1308059692382812, "end": **********.551727, "relative_end": **********.551727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.552403, "relative_start": 1.1314818859100342, "end": **********.552403, "relative_end": **********.552403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.553315, "relative_start": 1.1323938369750977, "end": **********.553315, "relative_end": **********.553315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.553582, "relative_start": 1.1326608657836914, "end": **********.553582, "relative_end": **********.553582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.553812, "relative_start": 1.1328909397125244, "end": **********.553812, "relative_end": **********.553812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.55406, "relative_start": 1.13313889503479, "end": **********.55406, "relative_end": **********.55406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.554323, "relative_start": 1.133401870727539, "end": **********.554323, "relative_end": **********.554323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.556196, "relative_start": 1.135274887084961, "end": **********.556196, "relative_end": **********.556196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.55716, "relative_start": 1.1362388134002686, "end": **********.55716, "relative_end": **********.55716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.557785, "relative_start": 1.1368639469146729, "end": **********.557785, "relative_end": **********.557785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.558391, "relative_start": 1.137470006942749, "end": **********.558391, "relative_end": **********.558391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.558946, "relative_start": 1.1380248069763184, "end": **********.558946, "relative_end": **********.558946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.559637, "relative_start": 1.1387159824371338, "end": **********.559637, "relative_end": **********.559637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.560283, "relative_start": 1.13936185836792, "end": **********.560283, "relative_end": **********.560283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.560829, "relative_start": 1.1399078369140625, "end": **********.560829, "relative_end": **********.560829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.56174, "relative_start": 1.1408188343048096, "end": **********.56174, "relative_end": **********.56174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.584501, "relative_start": 1.1635799407958984, "end": **********.584501, "relative_end": **********.584501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.587049, "relative_start": 1.1661279201507568, "end": **********.587049, "relative_end": **********.587049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.662026, "relative_start": 1.2411048412322998, "end": **********.662026, "relative_end": **********.662026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.67915, "relative_start": 1.2582290172576904, "end": **********.67915, "relative_end": **********.67915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.681757, "relative_start": 1.260835886001587, "end": **********.681757, "relative_end": **********.681757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.754939, "relative_start": 1.3340179920196533, "end": **********.754939, "relative_end": **********.754939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.778332, "relative_start": 1.3574109077453613, "end": **********.778332, "relative_end": **********.778332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.780912, "relative_start": 1.3599908351898193, "end": **********.780912, "relative_end": **********.780912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.853468, "relative_start": 1.432546854019165, "end": **********.853468, "relative_end": **********.853468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.872667, "relative_start": 1.4517459869384766, "end": **********.872667, "relative_end": **********.872667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.874888, "relative_start": 1.4539668560028076, "end": **********.874888, "relative_end": **********.874888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.947155, "relative_start": 1.5262339115142822, "end": **********.947155, "relative_end": **********.947155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.967939, "relative_start": 1.5470178127288818, "end": **********.967939, "relative_end": **********.967939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.970837, "relative_start": 1.5499160289764404, "end": **********.970837, "relative_end": **********.970837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.042004, "relative_start": 1.6210830211639404, "end": **********.042004, "relative_end": **********.042004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.063797, "relative_start": 1.6428759098052979, "end": **********.063797, "relative_end": **********.063797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.066241, "relative_start": 1.645319938659668, "end": **********.066241, "relative_end": **********.066241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.137249, "relative_start": 1.7163279056549072, "end": **********.137249, "relative_end": **********.137249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.161846, "relative_start": 1.7409248352050781, "end": **********.161846, "relative_end": **********.161846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.164404, "relative_start": 1.7434828281402588, "end": **********.164404, "relative_end": **********.164404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.244802, "relative_start": 1.823880910873413, "end": **********.244802, "relative_end": **********.244802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.265645, "relative_start": 1.8447239398956299, "end": **********.265645, "relative_end": **********.265645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.267906, "relative_start": 1.84698486328125, "end": **********.267906, "relative_end": **********.267906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.340556, "relative_start": 1.9196348190307617, "end": **********.340556, "relative_end": **********.340556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.360627, "relative_start": 1.9397058486938477, "end": **********.360627, "relative_end": **********.360627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.363471, "relative_start": 1.9425499439239502, "end": **********.363471, "relative_end": **********.363471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.437372, "relative_start": 2.016450881958008, "end": **********.437372, "relative_end": **********.437372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.456285, "relative_start": 2.0353639125823975, "end": **********.456285, "relative_end": **********.456285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.458561, "relative_start": 2.037639856338501, "end": **********.458561, "relative_end": **********.458561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.531708, "relative_start": 2.1107869148254395, "end": **********.531708, "relative_end": **********.531708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.533919, "relative_start": 2.1129980087280273, "end": **********.533919, "relative_end": **********.533919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.index", "start": **********.535049, "relative_start": 2.1141278743743896, "end": **********.535049, "relative_end": **********.535049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.536696, "relative_start": 2.1157748699188232, "end": **********.536696, "relative_end": **********.536696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.536941, "relative_start": 2.1160199642181396, "end": **********.536941, "relative_end": **********.536941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.537564, "relative_start": 2.116642951965332, "end": **********.537564, "relative_end": **********.537564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.537719, "relative_start": 2.116797924041748, "end": **********.537719, "relative_end": **********.537719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.538336, "relative_start": 2.117414951324463, "end": **********.538336, "relative_end": **********.538336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.539199, "relative_start": 2.1182780265808105, "end": **********.539199, "relative_end": **********.539199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.53952, "relative_start": 2.1185989379882812, "end": **********.53952, "relative_end": **********.53952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.539893, "relative_start": 2.118971824645996, "end": **********.539893, "relative_end": **********.539893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.540191, "relative_start": 2.119269847869873, "end": **********.540191, "relative_end": **********.540191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.540471, "relative_start": 2.1195499897003174, "end": **********.540471, "relative_end": **********.540471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.540762, "relative_start": 2.1198408603668213, "end": **********.540762, "relative_end": **********.540762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.541051, "relative_start": 2.1201298236846924, "end": **********.541051, "relative_end": **********.541051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.541303, "relative_start": 2.1203818321228027, "end": **********.541303, "relative_end": **********.541303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.541512, "relative_start": 2.120590925216675, "end": **********.541512, "relative_end": **********.541512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.54321, "relative_start": 2.122288942337036, "end": **********.54321, "relative_end": **********.54321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.544141, "relative_start": 2.1232199668884277, "end": **********.544141, "relative_end": **********.544141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.544845, "relative_start": 2.1239240169525146, "end": **********.544845, "relative_end": **********.544845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.545491, "relative_start": 2.124569892883301, "end": **********.545491, "relative_end": **********.545491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.54613, "relative_start": 2.125208854675293, "end": **********.54613, "relative_end": **********.54613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.546946, "relative_start": 2.1260249614715576, "end": **********.546946, "relative_end": **********.546946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.filters-header", "start": **********.602963, "relative_start": 2.182041883468628, "end": **********.602963, "relative_end": **********.602963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.603468, "relative_start": 2.182546854019165, "end": **********.603468, "relative_end": **********.603468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.615503, "relative_start": 2.194581985473633, "end": **********.615503, "relative_end": **********.615503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.615964, "relative_start": 2.195042848587036, "end": **********.615964, "relative_end": **********.615964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.628175, "relative_start": 2.207253932952881, "end": **********.628175, "relative_end": **********.628175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.629159, "relative_start": 2.208237886428833, "end": **********.629159, "relative_end": **********.629159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.629895, "relative_start": 2.2089738845825195, "end": **********.629895, "relative_end": **********.629895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.630323, "relative_start": 2.209401845932007, "end": **********.630323, "relative_end": **********.630323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.631, "relative_start": 2.2100789546966553, "end": **********.631, "relative_end": **********.631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.632216, "relative_start": 2.2112948894500732, "end": **********.632216, "relative_end": **********.632216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.633563, "relative_start": 2.212641954421997, "end": **********.633563, "relative_end": **********.633563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.634429, "relative_start": 2.213507890701294, "end": **********.634429, "relative_end": **********.634429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.636398, "relative_start": 2.2154769897460938, "end": **********.636398, "relative_end": **********.636398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.636699, "relative_start": 2.21577787399292, "end": **********.636699, "relative_end": **********.636699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.637085, "relative_start": 2.2161638736724854, "end": **********.637085, "relative_end": **********.637085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.637515, "relative_start": 2.2165939807891846, "end": **********.637515, "relative_end": **********.637515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.637776, "relative_start": 2.2168548107147217, "end": **********.637776, "relative_end": **********.637776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.638036, "relative_start": 2.2171149253845215, "end": **********.638036, "relative_end": **********.638036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.638385, "relative_start": 2.217463970184326, "end": **********.638385, "relative_end": **********.638385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.638622, "relative_start": 2.217700958251953, "end": **********.638622, "relative_end": **********.638622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.638928, "relative_start": 2.2180068492889404, "end": **********.638928, "relative_end": **********.638928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.639173, "relative_start": 2.218251943588257, "end": **********.639173, "relative_end": **********.639173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.639555, "relative_start": 2.2186338901519775, "end": **********.639555, "relative_end": **********.639555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.639939, "relative_start": 2.21901798248291, "end": **********.639939, "relative_end": **********.639939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.640762, "relative_start": 2.2198410034179688, "end": **********.640762, "relative_end": **********.640762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.641139, "relative_start": 2.2202179431915283, "end": **********.641139, "relative_end": **********.641139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.641819, "relative_start": 2.220897912979126, "end": **********.641819, "relative_end": **********.641819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.6422, "relative_start": 2.2212789058685303, "end": **********.6422, "relative_end": **********.6422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.642872, "relative_start": 2.2219510078430176, "end": **********.642872, "relative_end": **********.642872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.643175, "relative_start": 2.2222537994384766, "end": **********.643175, "relative_end": **********.643175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.64331, "relative_start": 2.222388982772827, "end": **********.64331, "relative_end": **********.64331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.643741, "relative_start": 2.2228198051452637, "end": **********.643741, "relative_end": **********.643741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar-logo", "start": **********.650938, "relative_start": 2.2300169467926025, "end": **********.650938, "relative_end": **********.650938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.651516, "relative_start": 2.2305948734283447, "end": **********.651516, "relative_end": **********.651516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.652294, "relative_start": 2.231372833251953, "end": **********.652294, "relative_end": **********.652294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.652714, "relative_start": 2.23179292678833, "end": **********.652714, "relative_end": **********.652714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.653518, "relative_start": 2.2325968742370605, "end": **********.653518, "relative_end": **********.653518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.654122, "relative_start": 2.233201026916504, "end": **********.654122, "relative_end": **********.654122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.654863, "relative_start": 2.2339420318603516, "end": **********.654863, "relative_end": **********.654863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.655433, "relative_start": 2.2345118522644043, "end": **********.655433, "relative_end": **********.655433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.65587, "relative_start": 2.2349488735198975, "end": **********.65587, "relative_end": **********.65587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.656423, "relative_start": 2.235502004623413, "end": **********.656423, "relative_end": **********.656423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.657232, "relative_start": 2.2363109588623047, "end": **********.657232, "relative_end": **********.657232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.658821, "relative_start": 2.2379000186920166, "end": **********.658821, "relative_end": **********.658821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.659591, "relative_start": 2.2386698722839355, "end": **********.659591, "relative_end": **********.659591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.660035, "relative_start": 2.2391138076782227, "end": **********.660035, "relative_end": **********.660035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.660542, "relative_start": 2.2396209239959717, "end": **********.660542, "relative_end": **********.660542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.660977, "relative_start": 2.240055799484253, "end": **********.660977, "relative_end": **********.660977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.661448, "relative_start": 2.2405269145965576, "end": **********.661448, "relative_end": **********.661448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.661962, "relative_start": 2.2410409450531006, "end": **********.661962, "relative_end": **********.661962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.662583, "relative_start": 2.24166202545166, "end": **********.662583, "relative_end": **********.662583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.663261, "relative_start": 2.242339849472046, "end": **********.663261, "relative_end": **********.663261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.663545, "relative_start": 2.242623805999756, "end": **********.663545, "relative_end": **********.663545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.663955, "relative_start": 2.2430338859558105, "end": **********.663955, "relative_end": **********.663955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.664211, "relative_start": 2.2432899475097656, "end": **********.664211, "relative_end": **********.664211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.664583, "relative_start": 2.243661880493164, "end": **********.664583, "relative_end": **********.664583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.664932, "relative_start": 2.2440109252929688, "end": **********.664932, "relative_end": **********.664932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.665421, "relative_start": 2.244499921798706, "end": **********.665421, "relative_end": **********.665421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.666203, "relative_start": 2.245281934738159, "end": **********.666203, "relative_end": **********.666203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.666442, "relative_start": 2.245520830154419, "end": **********.666442, "relative_end": **********.666442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.669776, "relative_start": 2.248854875564575, "end": **********.669776, "relative_end": **********.669776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.670208, "relative_start": 2.2492868900299072, "end": **********.670208, "relative_end": **********.670208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.672819, "relative_start": 2.2518978118896484, "end": **********.672957, "relative_end": **********.672957, "duration": 0.0001380443572998047, "duration_str": "138μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.692834, "relative_start": 2.2719128131866455, "end": **********.692885, "relative_end": **********.692885, "duration": 5.1021575927734375e-05, "duration_str": "51μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52449360, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 154, "nb_templates": 154, "templates": [{"name": "1x filament-panels::resources.pages.list-records", "param_count": null, "params": [], "start": **********.476895, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/resources/pages/list-records.blade.phpfilament-panels::resources.pages.list-records", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fresources%2Fpages%2Flist-records.blade.php&line=1", "ajax": false, "filename": "list-records.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::resources.pages.list-records"}, {"name": "1x filament-panels::components.resources.tabs", "param_count": null, "params": [], "start": **********.478009, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/resources/tabs.blade.phpfilament-panels::components.resources.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fresources%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.resources.tabs"}, {"name": "4x filament::components.input.index", "param_count": null, "params": [], "start": **********.530958, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.input.index"}, {"name": "6x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.531254, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament::components.input.wrapper"}, {"name": "31x filament::components.icon", "param_count": null, "params": [], "start": **********.532324, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 31, "name_original": "filament::components.icon"}, {"name": "4x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.533185, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.loading-indicator"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.535526, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "7x filament::components.icon-button", "param_count": null, "params": [], "start": **********.535968, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 7, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": **********.537326, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "1x filament::components.link", "param_count": null, "params": [], "start": **********.538561, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.link"}, {"name": "2x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.543498, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "5x filament::components.grid.column", "param_count": null, "params": [], "start": **********.544855, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.grid.column"}, {"name": "3x filament::components.grid.index", "param_count": null, "params": [], "start": **********.549238, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.grid.index"}, {"name": "1x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.552394, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "2x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.554316, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.index"}, {"name": "10x filament.resources.xdsl-service-resource.pages.status-column", "param_count": null, "params": [], "start": **********.584487, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/status-column.blade.phpfilament.resources.xdsl-service-resource.pages.status-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Fstatus-column.blade.php&line=1", "ajax": false, "filename": "status-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.status-column"}, {"name": "10x filament.resources.xdsl-service-resource.pages.location-column", "param_count": null, "params": [], "start": **********.587037, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.phpfilament.resources.xdsl-service-resource.pages.location-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=1", "ajax": false, "filename": "location-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.location-column"}, {"name": "10x __components::d14a7696a048e3b36f9bc9179acb02b3", "param_count": null, "params": [], "start": **********.662011, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/d14a7696a048e3b36f9bc9179acb02b3.blade.php__components::d14a7696a048e3b36f9bc9179acb02b3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fd14a7696a048e3b36f9bc9179acb02b3.blade.php&line=1", "ajax": false, "filename": "d14a7696a048e3b36f9bc9179acb02b3.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::d14a7696a048e3b36f9bc9179acb02b3"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.5339, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x filament::components.pagination.index", "param_count": null, "params": [], "start": **********.535031, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/index.blade.phpfilament::components.pagination.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.pagination.index"}, {"name": "2x filament::components.input.select", "param_count": null, "params": [], "start": **********.536678, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/select.blade.phpfilament::components.input.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.select"}, {"name": "1x filament::components.button.index", "param_count": null, "params": [], "start": **********.538328, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.button.index"}, {"name": "7x filament::components.pagination.item", "param_count": null, "params": [], "start": **********.539512, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/item.blade.phpfilament::components.pagination.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "filament::components.pagination.item"}, {"name": "5x filament::components.modal.index", "param_count": null, "params": [], "start": **********.543199, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.546937, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament.resources.xdsl-service-resource.pages.filters-header", "param_count": null, "params": [], "start": **********.602948, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/filters-header.blade.phpfilament.resources.xdsl-service-resource.pages.filters-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Ffilters-header.blade.php&line=1", "ajax": false, "filename": "filters-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.resources.xdsl-service-resource.pages.filters-header"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.603457, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.615492, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.615954, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.628164, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x __components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.632201, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e93d402ed1f3da8a07f4840136a03cb"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.633553, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.63442, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.63639, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.63669, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.637507, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.637767, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.639545, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "3x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.63993, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.643733, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.sidebar-logo", "param_count": null, "params": [], "start": **********.650919, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar-logo.blade.phpfilament-panels::components.sidebar-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar-logo.blade.php&line=1", "ajax": false, "filename": "sidebar-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar-logo"}, {"name": "3x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.654113, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.sidebar.group"}, {"name": "6x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.654855, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.664923, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.665412, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.666193, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.666431, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.670199, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}]}, "queries": {"count": 61, "nb_statements": 61, "nb_visible_statements": 61, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.1712399999999998, "accumulated_duration_str": "1.17s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'dvSi35OHDvDUJbcye5NgLWTAYMbi1v72XW9aYAyq'", "type": "query", "params": [], "bindings": ["dvSi35OHDvDUJbcye5NgLWTAYMbi1v72XW9aYAyq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.048015, "duration": 0.07634, "duration_str": "76.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 6.518}, {"sql": "select top 1 * from [users] where [id] = 15", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.127891, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 6.518, "width_percent": 1.645}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.149713, "duration": 0.01883, "duration_str": "18.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 8.163, "width_percent": 1.608}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:15')", "type": "query", "params": [], "bindings": ["filament-excel:exports:15"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.172087, "duration": 0.01858, "duration_str": "18.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 9.771, "width_percent": 1.586}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:15', 'illuminate:cache:flexible:created:filament-excel:exports:15')", "type": "query", "params": [], "bindings": ["filament-excel:exports:15", "illuminate:cache:flexible:created:filament-excel:exports:15"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.19157, "duration": 0.01908, "duration_str": "19.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 11.357, "width_percent": 1.629}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 15 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [15, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.212044, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 12.986, "width_percent": 1.644}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.23472, "duration": 0.01837, "duration_str": "18.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 14.631, "width_percent": 1.568}, {"sql": "select * from [cache] where [key] in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.2537801, "duration": 0.018699999999999998, "duration_str": "18.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 16.199, "width_percent": 1.597}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.273264, "duration": 0.01859, "duration_str": "18.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 17.796, "width_percent": 1.587}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.292644, "duration": 0.019190000000000002, "duration_str": "19.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 19.383, "width_percent": 1.638}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.312956, "duration": 0.018969999999999997, "duration_str": "18.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 21.021, "width_percent": 1.62}, {"sql": "select * from [cache] where [key] in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.341835, "duration": 0.04466, "duration_str": "44.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 22.641, "width_percent": 3.813}, {"sql": "select [permissions].*, [model_has_permissions].[model_id] as [pivot_model_id], [model_has_permissions].[permission_id] as [pivot_permission_id], [model_has_permissions].[model_type] as [pivot_model_type] from [permissions] inner join [model_has_permissions] on [permissions].[id] = [model_has_permissions].[permission_id] where [model_has_permissions].[model_id] in (15) and [model_has_permissions].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.3912692, "duration": 0.019399999999999997, "duration_str": "19.4ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "nourtel", "explain": null, "start_percent": 26.454, "width_percent": 1.656}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (15) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.411716, "duration": 0.01918, "duration_str": "19.18ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "nourtel", "explain": null, "start_percent": 28.11, "width_percent": 1.638}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 15 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [15, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 147}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.435087, "duration": 0.019969999999999998, "duration_str": "19.97ms", "memory": 0, "memory_str": null, "filename": "XDSLServiceResource.php:147", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource.php&line=147", "ajax": false, "filename": "XDSLServiceResource.php", "line": "147"}, "connection": "nourtel", "explain": null, "start_percent": 29.748, "width_percent": 1.705}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [created_by] = 15", "type": "query", "params": [], "bindings": ["xdsl", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.487894, "duration": 0.018760000000000002, "duration_str": "18.76ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "nourtel", "explain": null, "start_percent": 31.453, "width_percent": 1.602}, {"sql": "select top 10 * from [mesures] where [mesure_type] = 'xdsl' and [created_by] = 15 order by [mesure_date] desc", "type": "query", "params": [], "bindings": ["xdsl", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.507761, "duration": 0.01989, "duration_str": "19.89ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 33.055, "width_percent": 1.698}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5882409, "duration": 0.01729, "duration_str": "17.29ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 34.753, "width_percent": 1.476}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6063821, "duration": 0.01717, "duration_str": "17.17ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 36.229, "width_percent": 1.466}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.625287, "duration": 0.017, "duration_str": "17ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 37.695, "width_percent": 1.451}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.643455, "duration": 0.01686, "duration_str": "16.86ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 39.147, "width_percent": 1.44}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.682599, "duration": 0.01748, "duration_str": "17.48ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 40.586, "width_percent": 1.492}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.701169, "duration": 0.01724, "duration_str": "17.24ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 42.078, "width_percent": 1.472}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.719154, "duration": 0.01705, "duration_str": "17.05ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 43.55, "width_percent": 1.456}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.737002, "duration": 0.01694, "duration_str": "16.94ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 45.006, "width_percent": 1.446}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.781486, "duration": 0.01726, "duration_str": "17.26ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 46.452, "width_percent": 1.474}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.799831, "duration": 0.01691, "duration_str": "16.91ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 47.926, "width_percent": 1.444}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8174632, "duration": 0.016980000000000002, "duration_str": "16.98ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 49.37, "width_percent": 1.45}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.835185, "duration": 0.0176, "duration_str": "17.6ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 50.82, "width_percent": 1.503}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.875392, "duration": 0.01688, "duration_str": "16.88ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 52.322, "width_percent": 1.441}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.893147, "duration": 0.01762, "duration_str": "17.62ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 53.764, "width_percent": 1.504}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.911835, "duration": 0.01723, "duration_str": "17.23ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 55.268, "width_percent": 1.471}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.929857, "duration": 0.01661, "duration_str": "16.61ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 56.739, "width_percent": 1.418}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9713821, "duration": 0.016800000000000002, "duration_str": "16.8ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 58.157, "width_percent": 1.434}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9888802, "duration": 0.01677, "duration_str": "16.77ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 59.592, "width_percent": 1.432}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.006412, "duration": 0.017, "duration_str": "17ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 61.023, "width_percent": 1.451}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.024085, "duration": 0.01719, "duration_str": "17.19ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 62.475, "width_percent": 1.468}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0667322, "duration": 0.01629, "duration_str": "16.29ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 63.942, "width_percent": 1.391}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0837991, "duration": 0.01643, "duration_str": "16.43ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 65.333, "width_percent": 1.403}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.100994, "duration": 0.0175, "duration_str": "17.5ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 66.736, "width_percent": 1.494}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.119447, "duration": 0.01701, "duration_str": "17.01ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 68.23, "width_percent": 1.452}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.164933, "duration": 0.01688, "duration_str": "16.88ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 69.683, "width_percent": 1.441}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.1827059, "duration": 0.02461, "duration_str": "24.61ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 71.124, "width_percent": 2.101}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2079859, "duration": 0.01812, "duration_str": "18.12ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 73.225, "width_percent": 1.547}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.226877, "duration": 0.01702, "duration_str": "17.02ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 74.772, "width_percent": 1.453}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.268372, "duration": 0.01734, "duration_str": "17.34ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 76.225, "width_percent": 1.48}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2864058, "duration": 0.01694, "duration_str": "16.94ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 77.706, "width_percent": 1.446}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.304177, "duration": 0.01696, "duration_str": "16.96ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 79.152, "width_percent": 1.448}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.322087, "duration": 0.01753, "duration_str": "17.53ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 80.6, "width_percent": 1.497}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.363976, "duration": 0.01719, "duration_str": "17.19ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 82.097, "width_percent": 1.468}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3820019, "duration": 0.01724, "duration_str": "17.24ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 83.564, "width_percent": 1.472}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4009142, "duration": 0.01732, "duration_str": "17.32ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 85.036, "width_percent": 1.479}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.419173, "duration": 0.01742, "duration_str": "17.42ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 86.515, "width_percent": 1.487}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4590409, "duration": 0.01798, "duration_str": "17.98ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 88.002, "width_percent": 1.535}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.477863, "duration": 0.01695, "duration_str": "16.95ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 89.538, "width_percent": 1.447}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '5' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.495639, "duration": 0.01714, "duration_str": "17.14ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 90.985, "width_percent": 1.463}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.513554, "duration": 0.016980000000000002, "duration_str": "16.98ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 92.448, "width_percent": 1.45}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 15 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [15, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, {"index": 15, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.547744, "duration": 0.01809, "duration_str": "18.09ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:57", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=57", "ajax": false, "filename": "ListXDSLServices.php", "line": "57"}, "connection": "nourtel", "explain": null, "start_percent": 93.898, "width_percent": 1.545}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [created_by] = 15", "type": "query", "params": [], "bindings": ["xdsl", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.566865, "duration": 0.01773, "duration_str": "17.73ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:97", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=97", "ajax": false, "filename": "ListXDSLServices.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 95.442, "width_percent": 1.514}, {"sql": "select count(distinct [num_ligne]) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [created_by] = 15 and [num_ligne] is not null", "type": "query", "params": [], "bindings": ["xdsl", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5852368, "duration": 0.01711, "duration_str": "17.11ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:103", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=103", "ajax": false, "filename": "ListXDSLServices.php", "line": "103"}, "connection": "nourtel", "explain": null, "start_percent": 96.956, "width_percent": 1.461}, {"sql": "update [sessions] set [payload] = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaHZxTGhQSG9MQ3JBUEJCYXBESkhIUmVkblFvOUprUVVac3FnTWZueiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzc6Imh0dHBzOi8vbm91cnRlbC50ZXN0L3gtZC1zLWwtc2VydmljZXMiO31zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxNTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDVLazFEN3BOdE5OaHU4d0VGc1V6dXVTbEVKc3RTc1Mud0hYdXZtU20zcC92VEJIei50UHpLIjt9', [last_activity] = **********, [user_id] = 15, [ip_address] = '127.0.0.1', [user_agent] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where [id] = 'dvSi35OHDvDUJbcye5NgLWTAYMbi1v72XW9aYAyq'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaHZxTGhQSG9MQ3JBUEJCYXBESkhIUmVkblFvOUprUVVac3FnTWZueiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzc6Imh0dHBzOi8vbm91cnRlbC50ZXN0L3gtZC1zLWwtc2VydmljZXMiO31zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxNTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJDVLazFEN3BOdE5OaHU4d0VGc1V6dXVTbEVKc3RTc1Mud0hYdXZtU20zcC92VEJIei50UHpLIjt9", **********, 15, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "dvSi35OHDvDUJbcye5NgLWTAYMbi1v72XW9aYAyq"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.673741, "duration": 0.018539999999999997, "duration_str": "18.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "nourtel", "explain": null, "start_percent": 98.417, "width_percent": 1.583}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Mesure": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}, "App\\Models\\Equipe": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FEquipe.php&line=1", "ajax": false, "filename": "Equipe.php", "line": "?"}}, "App\\Models\\Region": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FRegion.php&line=1", "ajax": false, "filename": "Region.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services #PcofLrEIs2aX4WhSFQoY": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"date_range\" => array:2 [\n        \"created_from\" => null\n        \"created_until\" => null\n      ]\n      \"num_ligne_filter\" => array:1 [\n        \"num_ligne\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services\"\n  \"component\" => \"App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices\"\n  \"id\" => \"PcofLrEIs2aX4WhSFQoY\"\n]", "filament.livewire.notifications #QiJp6JsVK0PnXNK333mK": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2671\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"QiJp6JsVK0PnXNK333mK\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 58, "messages": [{"message": "[\n  ability => reorder_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-432666962 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">reorder_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432666962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.434305, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-964061273 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964061273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.434697, "xdebug_link": null}, {"message": "[\n  ability => delete_any_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-974844905 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">delete_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974844905\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.469249, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102845098 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102845098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.469361, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701978308 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701978308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.564177, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=241),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1840107370 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=241)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=241)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840107370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.56429, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-62960682 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62960682\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.664587, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=242),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1726181862 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=242)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=242)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726181862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.664723, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1263575784 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263575784\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.758964, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=226),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-465332554 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=226)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=226)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465332554\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.759188, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-761692497 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761692497\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.856286, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=227),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1221065497 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=227)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=227)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221065497\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.856406, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-501460488 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501460488\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.949539, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=228),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-366380230 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=228)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=228)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366380230\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.949655, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1796039639 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796039639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.04441, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=222),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-66793436 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=222)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=222)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66793436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.044516, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-961906836 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961906836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.142076, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=223),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1035595465 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=223)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=223)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035595465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.142207, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-456716007 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456716007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.248421, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=224),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-946050789 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=224)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=224)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946050789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.248552, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-166690687 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166690687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.34362, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=225),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1113672184 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=225)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=225)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113672184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.343733, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2114045711 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114045711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.440324, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=220),\n  result => false,\n  user => 15,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1751501048 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=220)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=220)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751501048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.440439, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-341107698 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341107698\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.617217, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-993099400 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993099400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.618775, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-22790030 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22790030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.618877, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14930856 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14930856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.620471, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-33203666 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33203666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.620559, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1144220693 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144220693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.621478, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1213001589 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213001589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.621565, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-642768862 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642768862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.622296, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155841376 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155841376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.622384, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-839302938 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839302938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.623118, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1789692127 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789692127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.623219, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1645469815 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645469815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.623917, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 15,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-182278768 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182278768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624008, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1049369676 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049369676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624734, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1058033583 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058033583\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624813, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1209892217 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209892217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.626849, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 15,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2091472440 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091472440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.626973, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1509236877 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509236877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.644822, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1543093296 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543093296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.645623, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1577916936 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577916936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.64571, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1073441087 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073441087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.646576, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1033117001 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1033117001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.64667, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-921551098 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921551098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.647345, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1492971452 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492971452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.647426, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2115740514 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115740514\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.648099, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-578920721 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578920721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.648178, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1124865484 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124865484\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.648655, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-640927165 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-640927165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.648731, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-958054624 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958054624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.649212, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 15,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-468629782 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468629782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.64929, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-348520108 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348520108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.649808, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 15,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1206855239 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206855239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.649888, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-875857204 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875857204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.650518, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 15,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1897766613 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897766613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.650607, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices", "uri": "GET x-d-s-l-services", "controller": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices@render<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/x-d-s-l-services", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor", "duration": "2.28s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ik9nTXhqaDJuOUZPQ29NM0NxcStNdVE9PSIsInZhbHVlIjoiQk0wMXdKalF6SE4yNGVZRm5QaXlOdjdmWlEyRGhNZGRxRWVUVmQzK2g3cUZiQ3g4NnNXUVhGWVArS0xZQnIwOVR0TWlhRndJd2lyNDBRclhUS1NMZmxhRUtIaUcvaXlCOUVqOEhzc3Rjc1BrQUNraEl5UDNMM2p5WThyb2RjY0pIZ01JL24xZ0dvSENEdmxLWGR6bTdpMGRLbk9VZ3ZuTXBjeTh3c3VzSytTdzBoR2ppUFgzdWo0eXU4KytLeFpzNGRHUndhUXpRNm44Sk5JTzNnTEptc284T3A2UXhVb1F1NVlIalBDYWowRT0iLCJtYWMiOiI2MjEwMTcxNjE1NzM3NGU2MDcwNjk1MWRjMWFiNGNmNjc0YTY5OTFlN2E4MDhkZDcyYTU3OGY4NDYxYWNlZjY0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlYwaVhUeU81cUFOS2kzZHBPOENXbWc9PSIsInZhbHVlIjoiOXlhbGZXbG5RNkNFcXMvNzFIOFJkaHRBYjlIVzZMUU5tU2FYWkxKa01XM1BuZTlVU0s3OGxtV3Z1U0JsTjVsUzFCT016ZkZ1RTdWa0l3Mkl1TERTRHIzeXpzcTg3UEJJK0dkSHh4RGV5UUtSWUplVGdMOWtWSGoyTWJESWdLMjQiLCJtYWMiOiI1NGNhZTcyOGE0OGQyN2U5ZTUxMTI5ZDNlMjBlMTIyY2IxNTliMjIxMGY2OTRiYzI3Y2ViZTMzNzQ4YWY3MzE4IiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6ImxyZ3hZNnFXT1ZyWVNCY1RjTC9GcUE9PSIsInZhbHVlIjoiV3llUGtIWi9Ha1FuYzFDRDY4ZW9Ic2Q0MHVwdGxaRFJyOUcxYXdvZStBazZLdkFxbGc5M0VkK09CTE02OGJCOEpRdkhyaER1eG4ycVpna3NPNmZCMWxPUGFMY1NqdDZ0WUZTU0I1eVZqT2FXN080WXJBbDlLc2dIMWFkdXJadnQiLCJtYWMiOiIyYjg0YjA1OGM4ODIzYzdjODA4YzMyY2Y4NGQzN2MzMGMxZDdmNzgyMjAwYzZjOGI1NWUyOTkxOWE0ZjFhNGJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://nourtel.test/g-p-o-n-services</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1862765057 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">15|CyZ4MxeJwBSwaC0gh0BSIKEldoIpkpPNQUSaXiG56eVQMqoIglejFEpPNUNn|$2y$12$5Kk1D7pNtNNhu8wEFsUzuuSlEJstSsS.wHXuvmSm3p/vTBHz.tPzK</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hvqLhPHoLCrAPBBapDJHHRednQo9JkQUZsqgMfnz</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dvSi35OHDvDUJbcye5NgLWTAYMbi1v72XW9aYAyq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862765057\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-579784081 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 12:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImdVZm5xdnU2L1Q5bXN3WmhuMFE0R2c9PSIsInZhbHVlIjoiSlBJcWpzbXpCVWhRbmxJNklwLzFMSC9ITWpxQTIzT3Y2OVpiczVtUGpPUTA0eDMrSUtXUkpUTGJkdHk1UTVCbXJDY29kT0hxWUhiZXBxaEtXN0Y2eXVMbzBvWXJ1SFBkUmxjblZubFhFczNJMDVURFhIRHpPbEtlcEpYZXpGdXYiLCJtYWMiOiI4MTkwYzY0YzdjM2Y4ODhiZjliZDhmMWMyZjIzZTRkODY2MzNiZmU4OTM1YWM5MmI1YjhhM2ZkMGYxZDk0YmU0IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 14:36:40 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"457 characters\">line_analyzer_session=eyJpdiI6IjFLU0JHWlZ3QjNIUTFPcUJiMk1LUVE9PSIsInZhbHVlIjoiL0NMTlA2NGlMZUJTaTlUWTl5ZUpLTGtPdkhaajRGektmK01kNjNnWmJNdmNEWXVMUE90QVM4dnhwL3VEcEQwQlNVMTlJa1FGM2VReXhzdGlvSUVKRjFQVUlzNTRsV2pURXZXSGxqUTAvL3B2SHBKZDZNM2pmTEsvaHRvUCs0L2YiLCJtYWMiOiIxMTllOTFmN2I1NWRmZjQ0NDZkNWYzMjI4YzFiYzQ0OGJhNzlkMmRkNjBmNzZmZTRiNmExZTNhYzJiZmY4NTkxIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 14:36:40 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImdVZm5xdnU2L1Q5bXN3WmhuMFE0R2c9PSIsInZhbHVlIjoiSlBJcWpzbXpCVWhRbmxJNklwLzFMSC9ITWpxQTIzT3Y2OVpiczVtUGpPUTA0eDMrSUtXUkpUTGJkdHk1UTVCbXJDY29kT0hxWUhiZXBxaEtXN0Y2eXVMbzBvWXJ1SFBkUmxjblZubFhFczNJMDVURFhIRHpPbEtlcEpYZXpGdXYiLCJtYWMiOiI4MTkwYzY0YzdjM2Y4ODhiZjliZDhmMWMyZjIzZTRkODY2MzNiZmU4OTM1YWM5MmI1YjhhM2ZkMGYxZDk0YmU0IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 14:36:40 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"429 characters\">line_analyzer_session=eyJpdiI6IjFLU0JHWlZ3QjNIUTFPcUJiMk1LUVE9PSIsInZhbHVlIjoiL0NMTlA2NGlMZUJTaTlUWTl5ZUpLTGtPdkhaajRGektmK01kNjNnWmJNdmNEWXVMUE90QVM4dnhwL3VEcEQwQlNVMTlJa1FGM2VReXhzdGlvSUVKRjFQVUlzNTRsV2pURXZXSGxqUTAvL3B2SHBKZDZNM2pmTEsvaHRvUCs0L2YiLCJtYWMiOiIxMTllOTFmN2I1NWRmZjQ0NDZkNWYzMjI4YzFiYzQ0OGJhNzlkMmRkNjBmNzZmZTRiNmExZTNhYzJiZmY4NTkxIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 14:36:40 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579784081\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-680520617 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hvqLhPHoLCrAPBBapDJHHRednQo9JkQUZsqgMfnz</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://nourtel.test/x-d-s-l-services</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$5Kk1D7pNtNNhu8wEFsUzuuSlEJstSsS.wHXuvmSm3p/vTBHz.tPzK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680520617\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices"}, "badge": null}}