<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ConsultationLigneResource\Pages;
use App\Models\Mesure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Columns\TextColumn;

class ConsultationLigneResource extends Resource
{
    protected static ?string $model = Mesure::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-magnifying-glass';
    protected static ?string $navigationLabel = 'Consultation Ligne';
    protected static ?string $navigationGroup = 'Service GPON'; // Place under Service GPON group
    protected static ?int $navigationSort = 1;
    protected static ?string $slug = 'consultation-ligne';
    protected static ?string $navigationId = 'consultation-ligne';

    public static function canAccess(): bool
    {
        // Autoriser l'accès à tous les utilisateurs authentifiés
        return Auth::check();
    }

    public static function getNavigationUrl(): string
    {
        return static::getUrl('index');
    }

    public static function getNavigationActiveState(): bool
    {
        return request()->routeIs('filament.admin.resources.consultation-ligne.*');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('created_by')
                    ->relationship('creator', 'name')
                    ->required()
                    ->disabled()
                    ->default(Auth::id()),

                Forms\Components\Select::make('equipe_id')
                    ->relationship('equipe', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->equipe_id),

                Forms\Components\Select::make('region_id')
                    ->relationship('region', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->region_id),

                Forms\Components\Section::make('GPON Measurements')
                    ->schema([
                        Forms\Components\TextInput::make('g_rx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_tx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_voltage')
                            ->numeric()
                            ->suffix('V'),
                        Forms\Components\TextInput::make('g_bias_current')
                            ->numeric()
                            ->suffix('mA'),
                        Forms\Components\TextInput::make('g_pon_alarm_info')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Etat')
                    ->schema([
                        Forms\Components\Select::make('mesure_type')
                            ->options([
                                'gpon' => 'GPON',
                                'xdsl' => 'xDSL',
                                'both' => 'Both',
                            ])
                            ->required()
                            ->default('gpon')
                            ->disabled(),
                        Forms\Components\Select::make('mesure_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                            ])
                            ->required(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                Mesure::query()
                    ->where('mesure_type', 'gpon')
            )
            ->searchable(false)
            ->filtersTriggerAction(null)
            ->deferFilters() // Prevent auto-refresh when filters change
            ->emptyStateHeading('Aucune donnée à afficher')
            ->emptyStateDescription('Veuillez effectuer une recherche par le numéro de ligne.')
            ->emptyStateIcon('heroicon-o-document-magnifying-glass')
            ->columns([
                TextColumn::make('num_ligne')
                    ->label('Num Ligne')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(fn ($record) => $record->num_ligne),
                TextColumn::make('emplacement')
                    ->label('Emplacement')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(fn ($record) => $record->emplacement),
                TextColumn::make('g_rx_power')
                    ->label('Puissance Rx')
                    ->numeric(
                        decimalPlaces: 2,
                        decimalSeparator: '.',
                        thousandsSeparator: ',',
                    )
                    ->suffix(' dBm')
                    ->sortable(),
                TextColumn::make('mesure_status')
                    ->label('Etat')
                    ->formatStateUsing(function (string $state): string {
                        $color = $state === '1' ? '#16a34a' : '#dc2626';
                        return "<div style='width: 20px; height: 20px; border-radius: 50%; background-color: {$color}; margin: 0 auto;'></div>";
                    })
                    ->html(),
                TextColumn::make('creator.name')
                    ->label('Créé Par')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('equipe.nom')
                    ->label('Équipe')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('region.nom')
                    ->label('Région')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('mesure_date')
                    ->label('Date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\ViewColumn::make('Localisation')
                    ->view('filament.resources.consultation-ligne-resource.pages.location-column')
                    ->alignCenter(),
            ])
            ->defaultSort('mesure_date', 'desc')
            ->paginated()
            ->recordUrl(null)
            ->actions([])
            ->filters([
                Tables\Filters\Filter::make('num_ligne_filter')
                    ->form([
                        Forms\Components\TextInput::make('num_ligne')
                            ->label('Num Ligne')
                            ->placeholder('Recherche exacte par numéro'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['num_ligne'] ?? null,
                            fn (Builder $query, $numLigne): Builder => $query->where('num_ligne', '=', $numLigne),
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConsultationLignes::route('/'),
            'create' => Pages\CreateConsultationLigne::route('/create'),
            'edit' => Pages\EditConsultationLigne::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (Auth::user()->equipe_id && Auth::user()->region_id) {
            $query->where('equipe_id', Auth::user()->equipe_id)
                  ->where('region_id', Auth::user()->region_id);
        }

        return $query;
    }
}
