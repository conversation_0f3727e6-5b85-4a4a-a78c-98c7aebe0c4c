<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Region;
use App\Models\Equipe;
use Illuminate\Http\Request;

class RegionController extends Controller
{
    /**
     * Récupérer toutes les équipes d'une région spécifique
     */
    public function getEquipes($regionId)
    {
        $region = Region::findOrFail($regionId);
        $equipes = $region->equipes()->orderBy('nom')->get();
        
        return response()->json($equipes);
    }
    
    /**
     * Récupérer toutes les équipes
     */
    public function getAllEquipes()
    {
        $equipes = Equipe::orderBy('nom')->get();
        
        return response()->json($equipes);
    }
}
