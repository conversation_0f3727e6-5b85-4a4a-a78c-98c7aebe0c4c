<x-filament-panels::page>
    <div class="space-y-6">
        <x-filament::section>
            <x-slot name="heading">
                {{ __('Informations Personnelles') }}
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ __('Nom') }}
                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        {{ $this->getUser()->name }}
                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ __('Email') }}
                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        {{ $this->getUser()->email }}
                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ __('Rôle') }}
                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        {{ $this->getUserRole() }}
                    </div>
                </div>

                @php
                    $equipes = $this->getUserEquipes();
                @endphp

                @if(count($equipes['agent']) > 0)
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            {{ __('Membre des équipes') }}
                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            @foreach($equipes['agent'] as $index => $equipe)
                                {{ $equipe->nom }}{{ $index < count($equipes['agent']) - 1 ? ', ' : '' }}
                            @endforeach
                        </div>
                    </div>
                @endif

                @if(count($equipes['responsable']) > 0)
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            {{ __('Responsable des équipes') }}
                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            @foreach($equipes['responsable'] as $index => $equipe)
                                {{ $equipe->nom }}{{ $index < count($equipes['responsable']) - 1 ? ', ' : '' }}
                            @endforeach
                        </div>
                    </div>
                @endif

                @php
                    $regions = $this->getUserRegions();
                @endphp

                @if(count($regions) > 0)
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            {{ __('Régions') }}
                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            @foreach($regions as $index => $region)
                                {{ $region->nom }}{{ $index < count($regions) - 1 ? ', ' : '' }}
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </x-filament::section>

        <div class="mt-8">
            {{ $this->form }}

            <div class="mt-4 flex justify-end">
                <x-filament::button wire:click="updatePassword" type="submit">
                    {{ __('Mettre à jour') }}
                </x-filament::button>
            </div>
        </div>
    </div>
</x-filament-panels::page>
