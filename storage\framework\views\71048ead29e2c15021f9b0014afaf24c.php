<!--[if BLOCK]><![endif]--><?php if($getRecord()->lat): ?>
    <button
        type="button"
        onclick="showLocationMap(<?php echo e($getRecord()->id); ?>, <?php echo e($getRecord()->lat); ?>, <?php echo e($getRecord()->lng); ?>, '<?php echo e($getRecord()->emplacement); ?>', '<?php echo e($getRecord()->num_ligne); ?>', <?php echo e($getRecord()->g_rx_power ?? 'null'); ?>, <?php echo e($getRecord()->g_tx_power ?? 'null'); ?>, <?php echo e($getRecord()->g_voltage ?? 'null'); ?>, <?php echo e($getRecord()->g_bias_current ?? 'null'); ?>, '<?php echo e($getRecord()->g_pon_alarm_info ?? ''); ?>', '<?php echo e($getRecord()->creator->name ?? ''); ?>', '<?php echo e($getRecord()->equipe->nom ?? ''); ?>', '<?php echo e($getRecord()->region->nom ?? ''); ?>', '<?php echo e($getRecord()->mesure_status); ?>', '<?php echo e($getRecord()->mesure_date ? \Carbon\Carbon::parse($getRecord()->mesure_date)->format('d/m/Y H:i') : ''); ?>')"
        class="filament-button filament-button-size-sm bg-primary-600 hover:bg-primary-500 text-white inline-flex items-center justify-center py-1 px-3 rounded-lg font-medium gap-1 shadow focus:outline-none focus:ring-offset-2 focus:ring-2 focus:ring-primary-500"
    >
        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-map-pin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
        <span>Voir localisation</span>
    </button>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->

<?php /**PATH D:\Projects\nourtel\resources\views/filament/resources/gpon-service-resource/pages/location-column.blade.php ENDPATH**/ ?>