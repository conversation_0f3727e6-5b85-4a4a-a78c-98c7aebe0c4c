// Global variables to store map instances
window.consultationLocationMaps = {};
window.xdslLocationMaps = {};
window.consultationXDSLLocationMaps = {};
window.locationMaps = {};

// GPON Consultation Location Map
function showConsultationLocationMap(id, lat, lng, emplacement, numLigne, rxPower, txPower, voltage, biasCurrent, alarmInfo, creatorName, equipeName, regionName, status, mesureDate) {
    // Status is a string from the mesure_status column
    console.log("showConsultationLocationMap called with ID:", id);
    console.log("Status value:", status, "Type:", typeof status);

    // Ensure we're working with a string
    status = String(status);
    // Create modal HTML
    const modalId = `consultation-location-modal-${id}`;
    const mapId = `consultation-map-${id}`;

    // Create modal if it doesn't exist
    if (!document.getElementById(modalId)) {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'consultation-location-modal';

        // Format measurement values
        const formattedRxPower = rxPower !== null ? rxPower.toFixed(2) + ' dBm' : 'N/A';
        const formattedTxPower = txPower !== null ? txPower.toFixed(2) + ' dBm' : 'N/A';
        const formattedVoltage = voltage !== null ? voltage.toFixed(2) + ' V' : 'N/A';
        const formattedBiasCurrent = biasCurrent !== null ? biasCurrent.toFixed(2) + ' mA' : 'N/A';

        modal.innerHTML = `
            <div class="consultation-location-modal-content">
                <div class="consultation-location-modal-header">
                    <div class="flex items-center gap-2">
                        <h3 class="consultation-location-modal-title">Détails de localisation</h3>
                    </div>
                    <button type="button" onclick="closeConsultationLocationModal(${id})" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="consultation-location-modal-body">
                    <div class="consultation-location-info-grid">
                        <div class="consultation-location-details-container">
                            <div class="consultation-location-details">
                                <div class="consultation-detail-row">
                                    <dt class="font-medium w-32">Num Ligne :</dt>
                                    <dd>${numLigne || 'N/A'}</dd>
                                </div>
                                <div class="consultation-detail-row">
                                    <dt class="font-medium w-32">Emplacement :</dt>
                                    <dd>${emplacement}</dd>
                                </div>
                                <div class="consultation-detail-row">
                                    <dt class="font-medium w-32">Puissance RX :</dt>
                                    <dd>${formattedRxPower}</dd>
                                </div>
                                <div class="consultation-detail-row">
                                    <dt class="font-medium w-32">Etat :</dt>
                                    <dd>
                                        <div class="w-6 h-6 rounded-full inline-block" style="background-color: ${status === '1' ? '#16a34a' : '#dc2626'}; box-shadow: 0 0 0 2px rgba(0,0,0,0.2);"></div>
                                    </dd>
                                </div>
                                <div class="consultation-detail-row">
                                    <dt class="font-medium w-32">Date :</dt>
                                    <dd>${mesureDate || new Date().toLocaleDateString('fr-FR') + ' ' + new Date().toLocaleTimeString('fr-FR')}</dd>
                                </div>
                            </div>
                        </div>
                        <div class="consultation-location-map-container">
                            <div id="${mapId}" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    } else {
        document.getElementById(modalId).style.display = 'flex';
    }

    // Initialize map with slight delay to ensure container is ready
    setTimeout(() => {
        if (!window.consultationLocationMaps[id]) {
            console.log("Creating map with coordinates:", lat, lng);

            // Convert string coordinates to numbers if needed
            const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
            const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;

            try {
                const map = L.map(mapId).setView([latitude, longitude], 15);

                // Use Google Maps satellite tiles
                L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                    attribution: '© Google Maps'
                }).addTo(map);

                const marker = L.marker([latitude, longitude]).addTo(map);
                marker.bindPopup(emplacement).openPopup();

                window.consultationLocationMaps[id] = map;
                console.log("Map created successfully");
            } catch (error) {
                console.error("Error creating map:", error);
                document.getElementById(mapId).innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <p>Erreur de chargement de la carte. Veuillez réessayer.</p>
                        <p>Détails: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Ensure map is properly sized
        if (window.consultationLocationMaps[id]) {
            console.log("Invalidating map size");
            window.consultationLocationMaps[id].invalidateSize();
        }
    }, 1500); // Increased timeout for better reliability
}

function closeConsultationLocationModal(id) {
    const modalId = `consultation-location-modal-${id}`;
    document.getElementById(modalId).style.display = 'none';
}

// xDSL Consultation Location Map
function showConsultationXDSLLocationMap(id, lat, lng, emplacement, numLigne, mode, upStream, downStream, upMaxRate, downMaxRate, snrMarginUp, snrMarginDown, attenUp, attenDown, crcErrors, creatorName, equipeName, regionName, status, mesureDate) {
    // Status is a string from the mesure_status column
    console.log("showConsultationXDSLLocationMap called with ID:", id);

    // Ensure we're working with a string
    status = String(status);
    // Create modal HTML
    const modalId = `consultation-xdsl-location-modal-${id}`;
    const mapId = `consultation-xdsl-map-${id}`;

    // Create modal if it doesn't exist
    if (!document.getElementById(modalId)) {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'consultation-xdsl-location-modal';

        // Format measurement values
        const formattedUpStream = upStream !== null ? upStream.toFixed(2) + ' kbps' : 'N/A';
        const formattedDownStream = downStream !== null ? downStream.toFixed(2) + ' kbps' : 'N/A';
        const formattedUpMaxRate = upMaxRate !== null ? upMaxRate.toFixed(2) + ' kbps' : 'N/A';
        const formattedDownMaxRate = downMaxRate !== null ? downMaxRate.toFixed(2) + ' kbps' : 'N/A';
        const formattedSnrMarginUp = snrMarginUp !== null ? snrMarginUp.toFixed(2) + ' db' : 'N/A';
        const formattedSnrMarginDown = snrMarginDown !== null ? snrMarginDown.toFixed(2) + ' db' : 'N/A';
        const formattedAttenUp = attenUp !== null ? attenUp.toFixed(2) + ' db' : 'N/A';
        const formattedAttenDown = attenDown !== null ? attenDown.toFixed(2) + ' db' : 'N/A';
        const formattedCrcErrors = crcErrors !== null ? crcErrors : 'N/A';

        modal.innerHTML = `
            <div class="consultation-xdsl-location-modal-content">
                <div class="consultation-xdsl-location-modal-header">
                    <div class="flex items-center gap-2">
                        <h3 class="consultation-xdsl-location-modal-title">Détails de localisation</h3>
                    </div>
                    <button type="button" onclick="closeConsultationXDSLLocationModal(${id})" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="consultation-xdsl-location-modal-body">
                    <div class="consultation-xdsl-location-info-grid">
                        <div class="consultation-xdsl-location-details-container">
                            <div class="consultation-xdsl-location-details">
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Num Ligne :</dt>
                                    <dd>${numLigne || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Emplacement :</dt>
                                    <dd>${emplacement}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Mode :</dt>
                                    <dd>${mode || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Créé Par :</dt>
                                    <dd>${creatorName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Equipe :</dt>
                                    <dd>${equipeName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Région :</dt>
                                    <dd>${regionName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Latitude :</dt>
                                    <dd>${lat}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Longitude :</dt>
                                    <dd>${lng}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Etat :</dt>
                                    <dd>
                                        <div class="w-6 h-6 rounded-full inline-block" style="background-color: ${status === '1' ? '#16a34a' : '#dc2626'}; box-shadow: 0 0 0 2px rgba(0,0,0,0.2);"></div>
                                    </dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Date :</dt>
                                    <dd>${mesureDate || new Date().toLocaleDateString('fr-FR') + ' ' + new Date().toLocaleTimeString('fr-FR')}</dd>
                                </div>
                            </div>
                        </div>
                        <div class="consultation-xdsl-location-map-container">
                            <div id="${mapId}" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    } else {
        document.getElementById(modalId).style.display = 'flex';
    }

    // Initialize map with slight delay to ensure container is ready
    setTimeout(() => {
        if (!window.consultationXDSLLocationMaps[id]) {
            console.log("Creating map with coordinates:", lat, lng);

            // Convert string coordinates to numbers if needed
            const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
            const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;

            try {
                const map = L.map(mapId).setView([latitude, longitude], 15);

                // Use Google Maps satellite tiles
                L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                    attribution: '© Google Maps'
                }).addTo(map);

                const marker = L.marker([latitude, longitude]).addTo(map);
                marker.bindPopup(emplacement).openPopup();

                window.consultationXDSLLocationMaps[id] = map;
                console.log("Map created successfully");
            } catch (error) {
                console.error("Error creating map:", error);
                document.getElementById(mapId).innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <p>Erreur de chargement de la carte. Veuillez réessayer.</p>
                        <p>Détails: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Ensure map is properly sized
        if (window.consultationXDSLLocationMaps[id]) {
            console.log("Invalidating map size");
            window.consultationXDSLLocationMaps[id].invalidateSize();
        }
    }, 1500); // Increased timeout for better reliability
}

function closeConsultationXDSLLocationModal(id) {
    const modalId = `consultation-xdsl-location-modal-${id}`;
    document.getElementById(modalId).style.display = 'none';
}

// xDSL Service Location Map
function showXDSLLocationMap(id, lat, lng, emplacement, numLigne, mode, upStream, downStream, upMaxRate, downMaxRate, snrMarginUp, snrMarginDown, attenUp, attenDown, crcErrors, creatorName, equipeName, regionName, status, mesureDate) {
    // Status is a string from the mesure_status column
    console.log("showXDSLLocationMap called with ID:", id);

    // Ensure we're working with a string
    status = String(status);
    // Create modal HTML
    const modalId = `xdsl-location-modal-${id}`;
    const mapId = `xdsl-map-${id}`;

    // Create modal if it doesn't exist
    if (!document.getElementById(modalId)) {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'xdsl-location-modal';

        // Format measurement values
        const formattedUpStream = upStream !== null ? upStream.toFixed(2) + ' kbps' : 'N/A';
        const formattedDownStream = downStream !== null ? downStream.toFixed(2) + ' kbps' : 'N/A';
        const formattedUpMaxRate = upMaxRate !== null ? upMaxRate.toFixed(2) + ' kbps' : 'N/A';
        const formattedDownMaxRate = downMaxRate !== null ? downMaxRate.toFixed(2) + ' kbps' : 'N/A';
        const formattedSnrMarginUp = snrMarginUp !== null ? snrMarginUp.toFixed(2) + ' db' : 'N/A';
        const formattedSnrMarginDown = snrMarginDown !== null ? snrMarginDown.toFixed(2) + ' db' : 'N/A';
        const formattedAttenUp = attenUp !== null ? attenUp.toFixed(2) + ' db' : 'N/A';
        const formattedAttenDown = attenDown !== null ? attenDown.toFixed(2) + ' db' : 'N/A';
        const formattedCrcErrors = crcErrors !== null ? crcErrors : 'N/A';

        modal.innerHTML = `
            <div class="xdsl-location-modal-content">
                <div class="xdsl-location-modal-header">
                    <div class="flex items-center gap-2">
                        <h3 class="xdsl-location-modal-title">Détails de localisation</h3>
                    </div>
                    <button type="button" onclick="closeXDSLLocationModal(${id})" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="xdsl-location-modal-body">
                    <div class="xdsl-location-info-grid">
                        <div class="xdsl-location-details-container">
                            <div class="xdsl-location-details">
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Num Ligne :</dt>
                                    <dd>${numLigne || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Emplacement :</dt>
                                    <dd>${emplacement}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Mode :</dt>
                                    <dd>${mode || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Créé Par :</dt>
                                    <dd>${creatorName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Equipe :</dt>
                                    <dd>${equipeName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Région :</dt>
                                    <dd>${regionName || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Latitude :</dt>
                                    <dd>${lat}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Longitude :</dt>
                                    <dd>${lng}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Etat :</dt>
                                    <dd>
                                        <div class="w-6 h-6 rounded-full inline-block" style="background-color: ${status === '1' ? '#16a34a' : '#dc2626'}; box-shadow: 0 0 0 2px rgba(0,0,0,0.2);"></div>
                                    </dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Date :</dt>
                                    <dd>${mesureDate || new Date().toLocaleDateString('fr-FR') + ' ' + new Date().toLocaleTimeString('fr-FR')}</dd>
                                </div>
                            </div>
                        </div>
                        <div class="xdsl-location-map-container">
                            <div id="${mapId}" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    } else {
        document.getElementById(modalId).style.display = 'flex';
    }

    // Initialize map with slight delay to ensure container is ready
    setTimeout(() => {
        if (!window.xdslLocationMaps[id]) {
            console.log("Creating map with coordinates:", lat, lng);

            // Convert string coordinates to numbers if needed
            const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
            const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;

            try {
                const map = L.map(mapId).setView([latitude, longitude], 15);

                // Use Google Maps satellite tiles
                L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                    attribution: '© Google Maps'
                }).addTo(map);

                const marker = L.marker([latitude, longitude]).addTo(map);
                marker.bindPopup(emplacement).openPopup();

                window.xdslLocationMaps[id] = map;
                console.log("Map created successfully");
            } catch (error) {
                console.error("Error creating map:", error);
                document.getElementById(mapId).innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <p>Erreur de chargement de la carte. Veuillez réessayer.</p>
                        <p>Détails: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Ensure map is properly sized
        if (window.xdslLocationMaps[id]) {
            console.log("Invalidating map size");
            window.xdslLocationMaps[id].invalidateSize();
        }
    }, 1500); // Increased timeout for better reliability
}

function closeXDSLLocationModal(id) {
    const modalId = `xdsl-location-modal-${id}`;
    document.getElementById(modalId).style.display = 'none';
}

// GPON Service Location Map
function showLocationMap(id, lat, lng, emplacement, numLigne, rxPower, txPower, voltage, biasCurrent, alarmInfo, creatorName, equipeName, regionName, status, mesureDate) {
    // Status is a string from the mesure_status column
    console.log("showLocationMap called with ID:", id);

    // Ensure we're working with a string
    status = String(status);
    // Create modal HTML
    const modalId = `location-modal-${id}`;
    const mapId = `map-${id}`;

    // Create modal if it doesn't exist
    if (!document.getElementById(modalId)) {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'location-modal';

        // Format measurement values
        const formattedRxPower = rxPower !== null ? rxPower.toFixed(2) + ' dBm' : 'N/A';
        const formattedTxPower = txPower !== null ? txPower.toFixed(2) + ' dBm' : 'N/A';
        const formattedVoltage = voltage !== null ? voltage.toFixed(2) + ' V' : 'N/A';
        const formattedBiasCurrent = biasCurrent !== null ? biasCurrent.toFixed(2) + ' mA' : 'N/A';

        modal.innerHTML = `
            <div class="location-modal-content">
                <div class="location-modal-header">
                    <div class="flex items-center gap-2">
                        <h3 class="location-modal-title">Détails de localisation</h3>
                    </div>
                    <button type="button" onclick="closeLocationModal(${id})" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="location-modal-body">
                    <div class="location-info-grid">
                        <div class="location-details-container">
                            <div class="location-details">
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Num Ligne :</dt>
                                    <dd>${numLigne || 'N/A'}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Emplacement :</dt>
                                    <dd>${emplacement}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Puissance RX :</dt>
                                    <dd>${formattedRxPower}</dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Etat :</dt>
                                    <dd>
                                        <div class="w-6 h-6 rounded-full inline-block" style="background-color: ${status === '1' ? '#16a34a' : '#dc2626'}; box-shadow: 0 0 0 2px rgba(0,0,0,0.2);"></div>
                                    </dd>
                                </div>
                                <div class="detail-row">
                                    <dt class="font-medium w-32">Date :</dt>
                                    <dd>${mesureDate || new Date().toLocaleDateString('fr-FR') + ' ' + new Date().toLocaleTimeString('fr-FR')}</dd>
                                </div>
                            </div>
                        </div>
                        <div class="location-map-container">
                            <div id="${mapId}" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    } else {
        document.getElementById(modalId).style.display = 'flex';
    }

    // Initialize map with slight delay to ensure container is ready
    setTimeout(() => {
        if (!window.locationMaps) {
            window.locationMaps = {};
        }

        if (!window.locationMaps[id]) {
            console.log("Creating map with coordinates:", lat, lng);

            // Convert string coordinates to numbers if needed
            const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
            const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;

            try {
                const map = L.map(mapId).setView([latitude, longitude], 15);

                // Use Google Maps satellite tiles
                L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                    attribution: '© Google Maps'
                }).addTo(map);

                const marker = L.marker([latitude, longitude]).addTo(map);
                marker.bindPopup(emplacement).openPopup();

                window.locationMaps[id] = map;
                console.log("Map created successfully");
            } catch (error) {
                console.error("Error creating map:", error);
                document.getElementById(mapId).innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <p>Erreur de chargement de la carte. Veuillez réessayer.</p>
                        <p>Détails: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Ensure map is properly sized
        if (window.locationMaps[id]) {
            console.log("Invalidating map size");
            window.locationMaps[id].invalidateSize();
        }
    }, 1500); // Increased timeout for better reliability
}

function closeLocationModal(id) {
    // Si id est défini, fermer la modal spécifique
    if (id !== undefined) {
        const modalId = `location-modal-${id}`;
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            modalElement.style.display = 'none';
        }
    }
    // Sinon, essayer de fermer la modal générique du tableau de bord
    else {
        const dashboardModal = document.getElementById('location-modal');
        if (dashboardModal) {
            dashboardModal.classList.remove('active');
        }
    }
}

// Handle ESC key to close modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('[id^="consultation-location-modal-"], [id^="consultation-xdsl-location-modal-"], [id^="xdsl-location-modal-"], [id^="location-modal-"]');
        openModals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
});
