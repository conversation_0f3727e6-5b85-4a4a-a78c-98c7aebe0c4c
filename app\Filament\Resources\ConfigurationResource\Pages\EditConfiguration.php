<?php

namespace App\Filament\Resources\ConfigurationResource\Pages;

use App\Filament\Resources\ConfigurationResource;
use App\Models\Configuration;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditConfiguration extends EditRecord
{
    protected static string $resource = ConfigurationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // If the type is being changed, check if another record with the new type already exists
        if ($record->type !== $data['type']) {
            $existingConfig = Configuration::where('type', $data['type'])
                ->where('id', '!=', $record->id)
                ->first();

            if ($existingConfig) {
                // Update the existing record with the new data and delete the current one
                $existingConfig->update($data);
                $record->delete();
                return $existingConfig;
            }
        }

        // Otherwise, just update the current record
        $record->update($data);
        return $record;
    }
}
