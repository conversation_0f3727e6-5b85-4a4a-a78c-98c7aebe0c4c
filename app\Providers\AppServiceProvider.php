<?php

namespace App\Providers;

use App\Models\User;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        parent::register();
        FilamentView::registerRenderHook('panels::body.end', fn(): string => Blade::render("@vite('resources/js/app.js')"));
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        Gate::define('viewApiDocs', function (User $user) {
            return true;
        });
        // Gate::policy()
        Event::listen(function (\SocialiteProviders\Manager\SocialiteWasCalled $event) {
            $event->extendSocialite('discord', \SocialiteProviders\Google\Provider::class);
        });

        // Ajouter des éléments de navigation personnalisés pour les utilisateurs avec le rôle id=4
        Filament::serving(function () {
            if (Auth::check()) {
                $user = Auth::user();

                // Récupérer le rôle de l'utilisateur
                $userRole = DB::table('roles')
                    ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->where('model_has_roles.model_id', $user->id)
                    ->where('model_has_roles.model_type', get_class($user))
                    ->select('roles.id')
                    ->first();

                // Si l'utilisateur a le rôle id=4, ajouter les éléments de navigation
                if ($userRole && $userRole->id == 4) {
                    Filament::registerNavigationItems([
                        // GPON Service Navigation Items
                        NavigationItem::make('Historique Qualification')
                            ->url(route('filament.admin.resources.g-p-o-n-services.index'))
                            ->icon('heroicon-o-document-chart-bar')
                            ->group('Service GPON')
                            ->sort(2),
                        NavigationItem::make('Consultation Ligne')
                            ->url(route('filament.admin.resources.consultation-ligne.index'))
                            ->icon('heroicon-o-document-magnifying-glass')
                            ->group('Service GPON')
                            ->sort(1),

                        // xDSL Service Navigation Items
                        NavigationItem::make('Historique Qualification')
                            ->url('/admin/resources/x-d-s-l-services')
                            ->icon('heroicon-o-document-chart-bar')
                            ->group('Service xDSL')
                            ->sort(2),
                        NavigationItem::make('Consultation Ligne')
                            ->url('/admin/resources/consultation-ligne-xdsl')
                            ->icon('heroicon-o-document-magnifying-glass')
                            ->group('Service xDSL')
                            ->sort(1),
                    ]);
                }

                // Réorganiser les groupes de navigation
                Filament::registerNavigationGroups([
                    NavigationGroup::make()
                        ->label('Tableau de bord'),
                    NavigationGroup::make()
                        ->label('Service GPON'),
                    NavigationGroup::make()
                        ->label('Service xDSL'),
                    NavigationGroup::make()
                        ->label('Administration'),
                ]);
            }
        });
    }
}
