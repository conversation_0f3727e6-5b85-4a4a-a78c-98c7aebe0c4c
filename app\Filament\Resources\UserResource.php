<?php

namespace App\Filament\Resources;

use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use App\Filament\Exports\UserExporter;
use App\Filament\Imports\UserImporter;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ImportAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Actions\ExportBulkAction;
use App\Filament\Resources\UserResource\Pages;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists\Components\Section as InfolistSection;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 3;

    //menu item title is Utilisateurs
    public static function getNavigationLabel(): string
    {
        return __('Utilisateurs');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informations Utilisateur')
                    ->description('Informations de base de l\'utilisateur')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nom & Prénom')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('matricule')
                            ->label('Matricule')
                            ->numeric()
                            ->unique(table: 'users', ignorable: fn ($record) => $record)
                            ->validationMessages([
                                'unique' => 'Existe déjà',
                            ])
                            ->maxLength(20),

                        TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(table: 'users', ignorable: fn ($record) => $record)
                            ->validationMessages([
                                'unique' => 'Existe déjà',
                            ])
                            ->maxLength(255),

                        TextInput::make('password')
                            ->label('Mot de passe')
                            ->password()
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255),
                    ]),

                Section::make('Rôle et Équipe')
                    ->description('Attribution des rôles et équipes')
                    ->schema([
                        Select::make('roles')
                            ->label('Rôle')
                            ->relationship('roles', 'name')
                            ->preload()
                            ->searchable()
                            ->required(),

                        Select::make('equipes')
                            ->label('Équipes')
                            ->relationship('agents', 'nom')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->afterStateHydrated(function ($component, $state) {
                                // Récupérer les équipes en fonction du rôle de l'utilisateur
                                $record = $component->getRecord();
                                if ($record) {
                                    $userRoles = $record->roles->pluck('id')->toArray();

                                    // Si l'utilisateur est un chef d'équipe (id=3)
                                    if (in_array(3, $userRoles)) {
                                        $equipes = $record->responsables->pluck('id')->toArray();
                                        $component->state($equipes);
                                    }
                                    // Si l'utilisateur est un agent (id=4)
                                    elseif (in_array(4, $userRoles)) {
                                        $equipes = $record->agents->pluck('id')->toArray();
                                        $component->state($equipes);
                                    }
                                }
                            })
                            ->helperText('Sélectionnez les équipes auxquelles l\'utilisateur appartient'),
                    ]),
            ]);
    }

    public static function canCreate(): bool
    {
        return true;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nom & Prénom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('matricule')
                    ->label('Matricule')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('roles.name')
                    ->label('Rôle')
                    ->searchable(),

                Tables\Columns\TextColumn::make('agents.nom')
                    ->label('Équipes (membre)')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),

                Tables\Columns\TextColumn::make('responsables.nom')
                    ->label('Équipes (responsable)')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),
            ])
            ->filters([
                SelectFilter::make('roles')
                    ->label('Rôle')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\Filter::make('matricule')
                    ->form([
                        TextInput::make('matricule')
                            ->label('Matricule')
                            ->placeholder('Rechercher par matricule'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['matricule'],
                                fn (Builder $query, $matricule): Builder => $query->where('matricule', 'like', "%{$matricule}%"),
                            );
                    }),

                SelectFilter::make('agents')
                    ->label('Équipes (membre)')
                    ->relationship('agents', 'nom')
                    ->multiple()
                    ->preload(),

                SelectFilter::make('responsables')
                    ->label('Équipes (responsable)')
                    ->relationship('responsables', 'nom')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Modifier'),
                Tables\Actions\DeleteAction::make()
                    ->label('Supprimer'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make()
                    ->exporter(UserExporter::class)
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfolistSection::make('Informations Utilisateur')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Nom & Prénom'),
                        TextEntry::make('matricule')
                            ->label('Matricule'),
                        TextEntry::make('email')
                            ->label('Email'),
                    ])
                    ->columns(2),

                InfolistSection::make('Rôle et Équipes')
                    ->schema([
                        TextEntry::make('roles.name')
                            ->label('Rôle')
                            ->badge()
                            ->color('primary'),

                        TextEntry::make('agents.nom')
                            ->label('Équipes (membre)')
                            ->listWithLineBreaks()
                            ->bulleted(),

                        TextEntry::make('responsables.nom')
                            ->label('Équipes (responsable)')
                            ->listWithLineBreaks()
                            ->bulleted(),
                    ])
                    ->columns(2),
            ]);
    }
}
