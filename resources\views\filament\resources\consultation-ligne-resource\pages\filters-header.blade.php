<div class="flex flex-col gap-6 bg-white dark:bg-gray-900 p-6">
    <!-- Header with Title -->
    <div class="flex flex-col gap-6">
        <h1 class="text-2xl font-bold tracking-tight md:text-3xl">
            {{ __('Consultation Ligne') }}
        </h1>


        <!-- Filter Bar -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <!-- Responsive Filter Layout -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <!-- Num Ligne Filter -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Num Ligne</span>
                        <div class="relative flex-1">
                            <input
                                type="number"
                                id="num_ligne"
                                wire:model="tableFilters.num_ligne_filter.num_ligne"
                                placeholder="Identifiant de la ligne"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                            >
                            <div wire:loading wire:target="tableFilters.num_ligne_filter.num_ligne" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="animate-spin h-4 w-4 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap justify-center sm:justify-end gap-2">
                    <button
                        type="button"
                        wire:click="filter"
                        class="inline-flex items-center justify-center rounded-lg border border-primary-600 bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-primary-500 dark:hover:bg-primary-600 dark:focus:ring-offset-gray-800"
                    >
                     <span wire:loading.remove wire:target="filter">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            Rechercher
                        </span>
                        <span wire:loading wire:target="filter" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Recherche...
                        </span>
                    </button>

                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div
        wire:loading
        class="animate-pulse flex items-center text-primary-600 dark:text-primary-500"
    >
        <svg class="animate-spin -ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm font-medium">Chargement...</span>
    </div>
</div>

<!-- Table Loading Overlay -->
<div
    wire:loading
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    style="backdrop-filter: blur(2px);"
>
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl flex flex-col items-center">
        <svg class="animate-spin h-10 w-10 text-primary-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg font-medium text-gray-900 dark:text-gray-100">Chargement des données...</span>
        <span class="text-sm text-gray-500 dark:text-gray-400 mt-2">Veuillez patienter pendant que nous traitons votre demande</span>
    </div>
</div>
