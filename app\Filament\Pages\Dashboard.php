<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\StatsOverview;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $title = 'Tableau de bord';

    protected static ?int $navigationSort = -1; // Pour s'assurer qu'il apparaît en premier

    protected static ?string $navigationId = 'dashboard';

    public function getWidgets(): array
    {
        return [
            StatsOverview::class
        ];
    }

    public static function getNavigationActiveState(): bool
    {
        return request()->routeIs('filament.admin.pages.dashboard');
    }
}