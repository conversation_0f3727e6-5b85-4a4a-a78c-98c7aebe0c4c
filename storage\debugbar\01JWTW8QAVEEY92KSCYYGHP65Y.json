{"__meta": {"id": "01JWTW8QAVEEY92KSCYYGHP65Y", "datetime": "2025-06-03 12:29:10", "utime": **********.876021, "method": "GET", "uri": "/x-d-s-l-services", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748953747.729956, "end": **********.876033, "duration": 3.1460771560668945, "duration_str": "3.15s", "measures": [{"label": "Booting", "start": 1748953747.729956, "relative_start": 0, "end": **********.389102, "relative_end": **********.389102, "duration": 0.****************, "duration_str": "659ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.389119, "relative_start": 0.****************, "end": **********.876034, "relative_end": 9.5367431640625e-07, "duration": 2.***************, "duration_str": "2.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.646116, "relative_start": 0.****************, "end": **********.650315, "relative_end": **********.650315, "duration": 0.004199028015136719, "duration_str": "4.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::resources.pages.list-records", "start": **********.137889, "relative_start": 1.***************, "end": **********.137889, "relative_end": **********.137889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.resources.tabs", "start": **********.139317, "relative_start": 1.****************, "end": **********.139317, "relative_end": **********.139317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.237242, "relative_start": 1.5072860717773438, "end": **********.237242, "relative_end": **********.237242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.237549, "relative_start": 1.5075931549072266, "end": **********.237549, "relative_end": **********.237549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.238781, "relative_start": 1.5088250637054443, "end": **********.238781, "relative_end": **********.238781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.240438, "relative_start": 1.5104820728302002, "end": **********.240438, "relative_end": **********.240438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.244046, "relative_start": 1.5140900611877441, "end": **********.244046, "relative_end": **********.244046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.244492, "relative_start": 1.5145361423492432, "end": **********.244492, "relative_end": **********.244492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.245581, "relative_start": 1.515625, "end": **********.245581, "relative_end": **********.245581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": **********.246294, "relative_start": 1.5163381099700928, "end": **********.246294, "relative_end": **********.246294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": **********.248748, "relative_start": 1.5187921524047852, "end": **********.248748, "relative_end": **********.248748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.249814, "relative_start": 1.5198581218719482, "end": **********.249814, "relative_end": **********.249814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.250089, "relative_start": 1.5201330184936523, "end": **********.250089, "relative_end": **********.250089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.253472, "relative_start": 1.5235161781311035, "end": **********.253472, "relative_end": **********.253472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.254128, "relative_start": 1.524172067642212, "end": **********.254128, "relative_end": **********.254128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.255873, "relative_start": 1.5259170532226562, "end": **********.255873, "relative_end": **********.255873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.258519, "relative_start": 1.5285630226135254, "end": **********.258519, "relative_end": **********.258519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.259859, "relative_start": 1.5299031734466553, "end": **********.259859, "relative_end": **********.259859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.260159, "relative_start": 1.530203104019165, "end": **********.260159, "relative_end": **********.260159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.261202, "relative_start": 1.5312461853027344, "end": **********.261202, "relative_end": **********.261202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.262298, "relative_start": 1.5323421955108643, "end": **********.262298, "relative_end": **********.262298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.262678, "relative_start": 1.532721996307373, "end": **********.262678, "relative_end": **********.262678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.263143, "relative_start": 1.5331871509552002, "end": **********.263143, "relative_end": **********.263143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.265099, "relative_start": 1.5351431369781494, "end": **********.265099, "relative_end": **********.265099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.265314, "relative_start": 1.535358190536499, "end": **********.265314, "relative_end": **********.265314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.266044, "relative_start": 1.536087989807129, "end": **********.266044, "relative_end": **********.266044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.267318, "relative_start": 1.5373620986938477, "end": **********.267318, "relative_end": **********.267318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.267649, "relative_start": 1.5376930236816406, "end": **********.267649, "relative_end": **********.267649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.268369, "relative_start": 1.5384130477905273, "end": **********.268369, "relative_end": **********.268369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": **********.268785, "relative_start": 1.5388290882110596, "end": **********.268785, "relative_end": **********.268785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.269267, "relative_start": 1.539311170578003, "end": **********.269267, "relative_end": **********.269267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.271429, "relative_start": 1.541473150253296, "end": **********.271429, "relative_end": **********.271429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.272254, "relative_start": 1.5422980785369873, "end": **********.272254, "relative_end": **********.272254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.272847, "relative_start": 1.542891025543213, "end": **********.272847, "relative_end": **********.272847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.273593, "relative_start": 1.5436370372772217, "end": **********.273593, "relative_end": **********.273593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.274776, "relative_start": 1.5448200702667236, "end": **********.274776, "relative_end": **********.274776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.2759, "relative_start": 1.5459439754486084, "end": **********.2759, "relative_end": **********.2759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.276644, "relative_start": 1.5466880798339844, "end": **********.276644, "relative_end": **********.276644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.277293, "relative_start": 1.5473370552062988, "end": **********.277293, "relative_end": **********.277293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.278449, "relative_start": 1.5484931468963623, "end": **********.278449, "relative_end": **********.278449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.307742, "relative_start": 1.5777862071990967, "end": **********.307742, "relative_end": **********.307742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.312133, "relative_start": 1.5821771621704102, "end": **********.312133, "relative_end": **********.312133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.45315, "relative_start": 1.7231941223144531, "end": **********.45315, "relative_end": **********.45315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.478265, "relative_start": 1.7483091354370117, "end": **********.478265, "relative_end": **********.478265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.480254, "relative_start": 1.750298023223877, "end": **********.480254, "relative_end": **********.480254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.564269, "relative_start": 1.834313154220581, "end": **********.564269, "relative_end": **********.564269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.591377, "relative_start": 1.8614211082458496, "end": **********.591377, "relative_end": **********.591377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.595924, "relative_start": 1.8659679889678955, "end": **********.595924, "relative_end": **********.595924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.735158, "relative_start": 2.005202054977417, "end": **********.735158, "relative_end": **********.735158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.759828, "relative_start": 2.029872179031372, "end": **********.759828, "relative_end": **********.759828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.763497, "relative_start": 2.033541202545166, "end": **********.763497, "relative_end": **********.763497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.867916, "relative_start": 2.137960195541382, "end": **********.867916, "relative_end": **********.867916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.899758, "relative_start": 2.169802188873291, "end": **********.899758, "relative_end": **********.899758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.902661, "relative_start": 2.1727051734924316, "end": **********.902661, "relative_end": **********.902661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.984935, "relative_start": 2.254979133605957, "end": **********.984935, "relative_end": **********.984935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.012334, "relative_start": 2.2823781967163086, "end": **********.012334, "relative_end": **********.012334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.014572, "relative_start": 2.284615993499756, "end": **********.014572, "relative_end": **********.014572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.097035, "relative_start": 2.367079019546509, "end": **********.097035, "relative_end": **********.097035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.11867, "relative_start": 2.388714075088501, "end": **********.11867, "relative_end": **********.11867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.120567, "relative_start": 2.390611171722412, "end": **********.120567, "relative_end": **********.120567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.20062, "relative_start": 2.4706640243530273, "end": **********.20062, "relative_end": **********.20062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.225781, "relative_start": 2.4958250522613525, "end": **********.225781, "relative_end": **********.225781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.228925, "relative_start": 2.498969078063965, "end": **********.228925, "relative_end": **********.228925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.358625, "relative_start": 2.628669023513794, "end": **********.358625, "relative_end": **********.358625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.382075, "relative_start": 2.6521191596984863, "end": **********.382075, "relative_end": **********.382075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.38564, "relative_start": 2.655683994293213, "end": **********.38564, "relative_end": **********.38564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.514532, "relative_start": 2.784576177597046, "end": **********.514532, "relative_end": **********.514532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.status-column", "start": **********.537703, "relative_start": 2.8077471256256104, "end": **********.537703, "relative_end": **********.537703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.location-column", "start": **********.54053, "relative_start": 2.8105740547180176, "end": **********.54053, "relative_end": **********.54053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d14a7696a048e3b36f9bc9179acb02b3", "start": **********.654122, "relative_start": 2.924166202545166, "end": **********.654122, "relative_end": **********.654122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.655978, "relative_start": 2.9260220527648926, "end": **********.655978, "relative_end": **********.655978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.index", "start": **********.658076, "relative_start": 2.9281201362609863, "end": **********.658076, "relative_end": **********.658076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.660208, "relative_start": 2.9302520751953125, "end": **********.660208, "relative_end": **********.660208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.660503, "relative_start": 2.930546998977661, "end": **********.660503, "relative_end": **********.660503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": **********.661164, "relative_start": 2.9312081336975098, "end": **********.661164, "relative_end": **********.661164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.661345, "relative_start": 2.931389093399048, "end": **********.661345, "relative_end": **********.661345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.662059, "relative_start": 2.932103157043457, "end": **********.662059, "relative_end": **********.662059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.663852, "relative_start": 2.933896064758301, "end": **********.663852, "relative_end": **********.663852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.664675, "relative_start": 2.9347190856933594, "end": **********.664675, "relative_end": **********.664675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.665351, "relative_start": 2.9353950023651123, "end": **********.665351, "relative_end": **********.665351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.665807, "relative_start": 2.9358510971069336, "end": **********.665807, "relative_end": **********.665807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.item", "start": **********.666168, "relative_start": 2.9362120628356934, "end": **********.666168, "relative_end": **********.666168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.666446, "relative_start": 2.936490058898926, "end": **********.666446, "relative_end": **********.666446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.668286, "relative_start": 2.9383301734924316, "end": **********.668286, "relative_end": **********.668286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.669342, "relative_start": 2.9393861293792725, "end": **********.669342, "relative_end": **********.669342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.670627, "relative_start": 2.94067120552063, "end": **********.670627, "relative_end": **********.670627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.672623, "relative_start": 2.942667007446289, "end": **********.672623, "relative_end": **********.672623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.674411, "relative_start": 2.944455146789551, "end": **********.674411, "relative_end": **********.674411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.675768, "relative_start": 2.9458119869232178, "end": **********.675768, "relative_end": **********.675768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.resources.xdsl-service-resource.pages.filters-header", "start": **********.750729, "relative_start": 3.02077317237854, "end": **********.750729, "relative_end": **********.750729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.751465, "relative_start": 3.0215091705322266, "end": **********.751465, "relative_end": **********.751465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.769592, "relative_start": 3.0396361351013184, "end": **********.769592, "relative_end": **********.769592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.770079, "relative_start": 3.0401229858398438, "end": **********.770079, "relative_end": **********.770079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.786409, "relative_start": 3.056452989578247, "end": **********.786409, "relative_end": **********.786409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.787342, "relative_start": 3.0573861598968506, "end": **********.787342, "relative_end": **********.787342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.788393, "relative_start": 3.0584371089935303, "end": **********.788393, "relative_end": **********.788393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.789666, "relative_start": 3.0597100257873535, "end": **********.789666, "relative_end": **********.789666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.790552, "relative_start": 3.060595989227295, "end": **********.790552, "relative_end": **********.790552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.791989, "relative_start": 3.062033176422119, "end": **********.791989, "relative_end": **********.791989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.793123, "relative_start": 3.063167095184326, "end": **********.793123, "relative_end": **********.793123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.794136, "relative_start": 3.0641801357269287, "end": **********.794136, "relative_end": **********.794136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.797953, "relative_start": 3.0679969787597656, "end": **********.797953, "relative_end": **********.797953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.798532, "relative_start": 3.0685760974884033, "end": **********.798532, "relative_end": **********.798532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.798939, "relative_start": 3.0689830780029297, "end": **********.798939, "relative_end": **********.798939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.799418, "relative_start": 3.0694620609283447, "end": **********.799418, "relative_end": **********.799418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.799732, "relative_start": 3.0697760581970215, "end": **********.799732, "relative_end": **********.799732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.800019, "relative_start": 3.0700631141662598, "end": **********.800019, "relative_end": **********.800019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.800367, "relative_start": 3.070411205291748, "end": **********.800367, "relative_end": **********.800367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.80063, "relative_start": 3.070674180984497, "end": **********.80063, "relative_end": **********.80063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.800969, "relative_start": 3.0710129737854004, "end": **********.800969, "relative_end": **********.800969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.801219, "relative_start": 3.071263074874878, "end": **********.801219, "relative_end": **********.801219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.801723, "relative_start": 3.0717670917510986, "end": **********.801723, "relative_end": **********.801723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.802338, "relative_start": 3.0723819732666016, "end": **********.802338, "relative_end": **********.802338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.804026, "relative_start": 3.0740699768066406, "end": **********.804026, "relative_end": **********.804026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.804589, "relative_start": 3.0746331214904785, "end": **********.804589, "relative_end": **********.804589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.805374, "relative_start": 3.075417995452881, "end": **********.805374, "relative_end": **********.805374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.805908, "relative_start": 3.0759520530700684, "end": **********.805908, "relative_end": **********.805908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.806641, "relative_start": 3.0766851902008057, "end": **********.806641, "relative_end": **********.806641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.80704, "relative_start": 3.0770840644836426, "end": **********.80704, "relative_end": **********.80704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.807181, "relative_start": 3.0772249698638916, "end": **********.807181, "relative_end": **********.807181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.808022, "relative_start": 3.078066110610962, "end": **********.808022, "relative_end": **********.808022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar-logo", "start": **********.821182, "relative_start": 3.091226100921631, "end": **********.821182, "relative_end": **********.821182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.821763, "relative_start": 3.0918071269989014, "end": **********.821763, "relative_end": **********.821763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.823121, "relative_start": 3.093165159225464, "end": **********.823121, "relative_end": **********.823121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.825092, "relative_start": 3.0951361656188965, "end": **********.825092, "relative_end": **********.825092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.826394, "relative_start": 3.09643816947937, "end": **********.826394, "relative_end": **********.826394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.827192, "relative_start": 3.097236156463623, "end": **********.827192, "relative_end": **********.827192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.828003, "relative_start": 3.0980470180511475, "end": **********.828003, "relative_end": **********.828003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.828511, "relative_start": 3.098555088043213, "end": **********.828511, "relative_end": **********.828511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.82894, "relative_start": 3.0989840030670166, "end": **********.82894, "relative_end": **********.82894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.829599, "relative_start": 3.0996429920196533, "end": **********.829599, "relative_end": **********.829599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.830927, "relative_start": 3.100970983505249, "end": **********.830927, "relative_end": **********.830927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.832123, "relative_start": 3.1021671295166016, "end": **********.832123, "relative_end": **********.832123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.833128, "relative_start": 3.1031720638275146, "end": **********.833128, "relative_end": **********.833128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.833705, "relative_start": 3.1037490367889404, "end": **********.833705, "relative_end": **********.833705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.834172, "relative_start": 3.1042160987854004, "end": **********.834172, "relative_end": **********.834172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.834575, "relative_start": 3.104619026184082, "end": **********.834575, "relative_end": **********.834575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.834978, "relative_start": 3.1050221920013428, "end": **********.834978, "relative_end": **********.834978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.835512, "relative_start": 3.105556011199951, "end": **********.835512, "relative_end": **********.835512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.836267, "relative_start": 3.106311082839966, "end": **********.836267, "relative_end": **********.836267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.837651, "relative_start": 3.1076951026916504, "end": **********.837651, "relative_end": **********.837651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.838909, "relative_start": 3.1089529991149902, "end": **********.838909, "relative_end": **********.838909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.839491, "relative_start": 3.109534978866577, "end": **********.839491, "relative_end": **********.839491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.839786, "relative_start": 3.109830141067505, "end": **********.839786, "relative_end": **********.839786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.840251, "relative_start": 3.110295057296753, "end": **********.840251, "relative_end": **********.840251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.840772, "relative_start": 3.11081600189209, "end": **********.840772, "relative_end": **********.840772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.841409, "relative_start": 3.111453056335449, "end": **********.841409, "relative_end": **********.841409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.842365, "relative_start": 3.1124091148376465, "end": **********.842365, "relative_end": **********.842365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.84273, "relative_start": 3.112774133682251, "end": **********.84273, "relative_end": **********.84273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.847638, "relative_start": 3.1176819801330566, "end": **********.847638, "relative_end": **********.847638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.848186, "relative_start": 3.118230104446411, "end": **********.848186, "relative_end": **********.848186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.851709, "relative_start": 3.121752977371216, "end": **********.852273, "relative_end": **********.852273, "duration": 0.0005640983581542969, "duration_str": "564μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.873897, "relative_start": 3.1439411640167236, "end": **********.873954, "relative_end": **********.873954, "duration": 5.698204040527344e-05, "duration_str": "57μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52339480, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 151, "nb_templates": 151, "templates": [{"name": "1x filament-panels::resources.pages.list-records", "param_count": null, "params": [], "start": **********.137871, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/resources/pages/list-records.blade.phpfilament-panels::resources.pages.list-records", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fresources%2Fpages%2Flist-records.blade.php&line=1", "ajax": false, "filename": "list-records.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::resources.pages.list-records"}, {"name": "1x filament-panels::components.resources.tabs", "param_count": null, "params": [], "start": **********.139304, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/resources/tabs.blade.phpfilament-panels::components.resources.tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fresources%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.resources.tabs"}, {"name": "4x filament::components.input.index", "param_count": null, "params": [], "start": **********.23723, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.input.index"}, {"name": "6x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.23754, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament::components.input.wrapper"}, {"name": "31x filament::components.icon", "param_count": null, "params": [], "start": **********.238764, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 31, "name_original": "filament::components.icon"}, {"name": "4x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.240399, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.loading-indicator"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.244035, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "7x filament::components.icon-button", "param_count": null, "params": [], "start": **********.244481, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 7, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": **********.246266, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "1x filament::components.link", "param_count": null, "params": [], "start": **********.248736, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.link"}, {"name": "2x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.255855, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "5x filament::components.grid.column", "param_count": null, "params": [], "start": **********.258505, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.grid.column"}, {"name": "3x filament::components.grid.index", "param_count": null, "params": [], "start": **********.262666, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.grid.index"}, {"name": "1x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.266035, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "2x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.269255, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.index"}, {"name": "10x filament.resources.xdsl-service-resource.pages.status-column", "param_count": null, "params": [], "start": **********.307728, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/status-column.blade.phpfilament.resources.xdsl-service-resource.pages.status-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Fstatus-column.blade.php&line=1", "ajax": false, "filename": "status-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.status-column"}, {"name": "10x filament.resources.xdsl-service-resource.pages.location-column", "param_count": null, "params": [], "start": **********.312119, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.phpfilament.resources.xdsl-service-resource.pages.location-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=1", "ajax": false, "filename": "location-column.blade.php", "line": "?"}, "render_count": 10, "name_original": "filament.resources.xdsl-service-resource.pages.location-column"}, {"name": "10x __components::d14a7696a048e3b36f9bc9179acb02b3", "param_count": null, "params": [], "start": **********.453137, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/d14a7696a048e3b36f9bc9179acb02b3.blade.php__components::d14a7696a048e3b36f9bc9179acb02b3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2Fd14a7696a048e3b36f9bc9179acb02b3.blade.php&line=1", "ajax": false, "filename": "d14a7696a048e3b36f9bc9179acb02b3.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::d14a7696a048e3b36f9bc9179acb02b3"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": **********.655958, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x filament::components.pagination.index", "param_count": null, "params": [], "start": **********.658052, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/index.blade.phpfilament::components.pagination.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.pagination.index"}, {"name": "2x filament::components.input.select", "param_count": null, "params": [], "start": **********.660197, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/input/select.blade.phpfilament::components.input.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.select"}, {"name": "1x filament::components.button.index", "param_count": null, "params": [], "start": **********.66205, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.button.index"}, {"name": "4x filament::components.pagination.item", "param_count": null, "params": [], "start": **********.664658, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/pagination/item.blade.phpfilament::components.pagination.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "filament::components.pagination.item"}, {"name": "5x filament::components.modal.index", "param_count": null, "params": [], "start": **********.668276, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.675754, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament.resources.xdsl-service-resource.pages.filters-header", "param_count": null, "params": [], "start": **********.750714, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/filters-header.blade.phpfilament.resources.xdsl-service-resource.pages.filters-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Ffilters-header.blade.php&line=1", "ajax": false, "filename": "filters-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.resources.xdsl-service-resource.pages.filters-header"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.751434, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.769578, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.770067, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.786389, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x __components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.791979, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e93d402ed1f3da8a07f4840136a03cb"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.793113, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.794102, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.79794, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.798522, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.799408, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.799722, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.801709, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "3x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.802323, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.808009, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.sidebar-logo", "param_count": null, "params": [], "start": **********.82117, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar-logo.blade.phpfilament-panels::components.sidebar-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar-logo.blade.php&line=1", "ajax": false, "filename": "sidebar-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar-logo"}, {"name": "3x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.827182, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.sidebar.group"}, {"name": "6x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.827993, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.840761, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.8414, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.842356, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.842718, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.848177, "type": "blade", "hash": "bladeD:\\Projects\\nourtel\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}]}, "queries": {"count": 63, "nb_statements": 63, "nb_visible_statements": 63, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.6010199999999997, "accumulated_duration_str": "1.6s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select top 1 * from [sessions] where [id] = 'L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz'", "type": "query", "params": [], "bindings": ["L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.6562312, "duration": 0.07087, "duration_str": "70.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 4.427}, {"sql": "select top 1 * from [users] where [id] = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.733767, "duration": 0.01846, "duration_str": "18.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 4.427, "width_percent": 1.153}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (14) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7580318, "duration": 0.020059999999999998, "duration_str": "20.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "nourtel", "explain": null, "start_percent": 5.58, "width_percent": 1.253}, {"sql": "select * from [cache] where [key] in ('filament-excel:exports:14')", "type": "query", "params": [], "bindings": ["filament-excel:exports:14"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.783696, "duration": 0.01858, "duration_str": "18.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 6.833, "width_percent": 1.161}, {"sql": "delete from [cache] where [key] in ('filament-excel:exports:14', 'illuminate:cache:flexible:created:filament-excel:exports:14')", "type": "query", "params": [], "bindings": ["filament-excel:exports:14", "illuminate:cache:flexible:created:filament-excel:exports:14"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.804856, "duration": 0.01847, "duration_str": "18.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "nourtel", "explain": null, "start_percent": 7.993, "width_percent": 1.154}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 14 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [14, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php", "line": 13}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php", "line": 14}], "start": **********.825237, "duration": 0.02112, "duration_str": "21.12ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:54", "source": {"index": 14, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\Projects\\nourtel\\app\\Providers\\AppServiceProvider.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FProviders%2FAppServiceProvider.php&line=54", "ajax": false, "filename": "AppServiceProvider.php", "line": "54"}, "connection": "nourtel", "explain": null, "start_percent": 9.147, "width_percent": 1.319}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.850533, "duration": 0.01874, "duration_str": "18.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 10.466, "width_percent": 1.171}, {"sql": "select * from [cache] where [key] in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.8701699, "duration": 0.01989, "duration_str": "19.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 11.636, "width_percent": 1.242}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.890951, "duration": 0.018690000000000002, "duration_str": "18.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 12.879, "width_percent": 1.167}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.910449, "duration": 0.01848, "duration_str": "18.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 14.046, "width_percent": 1.154}, {"sql": "select * from [cache] where [key] in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.929739, "duration": 0.019809999999999998, "duration_str": "19.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 15.2, "width_percent": 1.237}, {"sql": "select * from [cache] where [key] in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.961279, "duration": 0.05356, "duration_str": "53.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "nourtel", "explain": null, "start_percent": 16.438, "width_percent": 3.345}, {"sql": "select [permissions].*, [model_has_permissions].[model_id] as [pivot_model_id], [model_has_permissions].[permission_id] as [pivot_permission_id], [model_has_permissions].[model_type] as [pivot_model_type] from [permissions] inner join [model_has_permissions] on [permissions].[id] = [model_has_permissions].[permission_id] where [model_has_permissions].[model_id] in (14) and [model_has_permissions].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.0214958, "duration": 0.020059999999999998, "duration_str": "20.06ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "nourtel", "explain": null, "start_percent": 19.783, "width_percent": 1.253}, {"sql": "select [roles].*, [model_has_roles].[model_id] as [pivot_model_id], [model_has_roles].[role_id] as [pivot_role_id], [model_has_roles].[model_type] as [pivot_model_type] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] in (14) and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.042525, "duration": 0.02146, "duration_str": "21.46ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\Projects\\nourtel\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "nourtel", "explain": null, "start_percent": 21.036, "width_percent": 1.34}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 14 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [14, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 147}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.068975, "duration": 0.01948, "duration_str": "19.48ms", "memory": 0, "memory_str": null, "filename": "XDSLServiceResource.php:147", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource.php&line=147", "ajax": false, "filename": "XDSLServiceResource.php", "line": "147"}, "connection": "nourtel", "explain": null, "start_percent": 22.376, "width_percent": 1.217}, {"sql": "select [equipe_id] from [equipe_responsable] where [user_id] = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 162}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.0899148, "duration": 0.01868, "duration_str": "18.68ms", "memory": 0, "memory_str": null, "filename": "XDSLServiceResource.php:162", "source": {"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource.php&line=162", "ajax": false, "filename": "XDSLServiceResource.php", "line": "162"}, "connection": "nourtel", "explain": null, "start_percent": 23.593, "width_percent": 1.167}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3')", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.152297, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "nourtel", "explain": null, "start_percent": 24.76, "width_percent": 1.079}, {"sql": "select top 10 * from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3') order by [mesure_date] desc", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.172152, "duration": 0.06215, "duration_str": "62.15ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\Projects\\nourtel\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 25.839, "width_percent": 3.882}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3133981, "duration": 0.07648, "duration_str": "76.48ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 29.72, "width_percent": 4.777}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.390663, "duration": 0.019469999999999998, "duration_str": "19.47ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 34.497, "width_percent": 1.216}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.411817, "duration": 0.01977, "duration_str": "19.77ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 35.713, "width_percent": 1.235}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4326751, "duration": 0.018940000000000002, "duration_str": "18.94ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 36.948, "width_percent": 1.183}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.480752, "duration": 0.0212, "duration_str": "21.2ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 38.131, "width_percent": 1.324}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5027978, "duration": 0.019010000000000003, "duration_str": "19.01ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 39.455, "width_percent": 1.187}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.522449, "duration": 0.0198, "duration_str": "19.8ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 40.643, "width_percent": 1.237}, {"sql": "select top 1 * from [regions] where [regions].[id] = '1' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5431721, "duration": 0.0193, "duration_str": "19.3ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 41.88, "width_percent": 1.205}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.596728, "duration": 0.07614, "duration_str": "76.14ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 43.085, "width_percent": 4.756}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.673929, "duration": 0.019620000000000002, "duration_str": "19.62ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 47.841, "width_percent": 1.225}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6943119, "duration": 0.020050000000000002, "duration_str": "20.05ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 49.066, "width_percent": 1.252}, {"sql": "select top 1 * from [regions] where [regions].[id] = '1' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.71556, "duration": 0.01881, "duration_str": "18.81ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 50.319, "width_percent": 1.175}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7640169, "duration": 0.02216, "duration_str": "22.16ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 51.493, "width_percent": 1.384}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7868981, "duration": 0.02157, "duration_str": "21.57ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 52.878, "width_percent": 1.347}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.809448, "duration": 0.01989, "duration_str": "19.89ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 54.225, "width_percent": 1.242}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.831614, "duration": 0.03472, "duration_str": "34.72ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 55.467, "width_percent": 2.169}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9031532, "duration": 0.01891, "duration_str": "18.91ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 57.636, "width_percent": 1.181}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.922806, "duration": 0.01943, "duration_str": "19.43ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 58.817, "width_percent": 1.214}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.943106, "duration": 0.01852, "duration_str": "18.52ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 60.03, "width_percent": 1.157}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.964135, "duration": 0.02001, "duration_str": "20.01ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 61.187, "width_percent": 1.25}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.015045, "duration": 0.02017, "duration_str": "20.17ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 62.437, "width_percent": 1.26}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.035907, "duration": 0.0189, "duration_str": "18.9ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 63.697, "width_percent": 1.18}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.055673, "duration": 0.01918, "duration_str": "19.18ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 64.877, "width_percent": 1.198}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.075825, "duration": 0.019829999999999997, "duration_str": "19.83ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 66.075, "width_percent": 1.239}, {"sql": "select top 1 * from [users] where [users].[id] = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.121086, "duration": 0.02043, "duration_str": "20.43ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 67.314, "width_percent": 1.276}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (15) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.142536, "duration": 0.01864, "duration_str": "18.64ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 68.59, "width_percent": 1.164}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.161839, "duration": 0.01781, "duration_str": "17.81ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 69.754, "width_percent": 1.112}, {"sql": "select top 1 * from [regions] where [regions].[id] = '2' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.180675, "duration": 0.01875, "duration_str": "18.75ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 70.867, "width_percent": 1.171}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.229471, "duration": 0.07099, "duration_str": "70.99ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 72.038, "width_percent": 4.434}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.301131, "duration": 0.01909, "duration_str": "19.09ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 76.472, "width_percent": 1.192}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.32095, "duration": 0.01796, "duration_str": "17.96ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 77.664, "width_percent": 1.122}, {"sql": "select top 1 * from [regions] where [regions].[id] = '1' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3397648, "duration": 0.01709, "duration_str": "17.09ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 78.786, "width_percent": 1.067}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.386337, "duration": 0.07073, "duration_str": "70.73ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 79.853, "width_percent": 4.418}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4578948, "duration": 0.01709, "duration_str": "17.09ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 84.271, "width_percent": 1.067}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.477132, "duration": 0.01821, "duration_str": "18.21ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 85.339, "width_percent": 1.137}, {"sql": "select top 1 * from [regions] where [regions].[id] = '1' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.496346, "duration": 0.01756, "duration_str": "17.56ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 86.476, "width_percent": 1.097}, {"sql": "select top 1 * from [users] where [users].[id] = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.541005, "duration": 0.05619, "duration_str": "56.19ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 87.573, "width_percent": 3.51}, {"sql": "select * from [breezy_sessions] where [breezy_sessions].[authenticatable_id] in (11) and [breezy_sessions].[authenticatable_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.597875, "duration": 0.018760000000000002, "duration_str": "18.76ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 27, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 91.083, "width_percent": 1.172}, {"sql": "select top 1 * from [equipes] where [equipes].[id] = '3' and [equipes].[deleted_at] is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.617356, "duration": 0.017070000000000002, "duration_str": "17.07ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 92.254, "width_percent": 1.066}, {"sql": "select top 1 * from [regions] where [regions].[id] = '1' and [regions].[deleted_at] is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.635446, "duration": 0.01805, "duration_str": "18.05ms", "memory": 0, "memory_str": null, "filename": "filament.resources.xdsl-service-resource.pages.location-column:4", "source": {"index": 22, "namespace": "view", "name": "filament.resources.xdsl-service-resource.pages.location-column", "file": "D:\\Projects\\nourtel\\resources\\views/filament/resources/xdsl-service-resource/pages/location-column.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fresources%2Fviews%2Ffilament%2Fresources%2Fxdsl-service-resource%2Fpages%2Flocation-column.blade.php&line=4", "ajax": false, "filename": "location-column.blade.php", "line": "4"}, "connection": "nourtel", "explain": null, "start_percent": 93.321, "width_percent": 1.127}, {"sql": "select top 1 [roles].[id] from [roles] inner join [model_has_roles] on [roles].[id] = [model_has_roles].[role_id] where [model_has_roles].[model_id] = 14 and [model_has_roles].[model_type] = 'App\\Models\\User'", "type": "query", "params": [], "bindings": [14, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, {"index": 15, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.677094, "duration": 0.01764, "duration_str": "17.64ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:57", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=57", "ajax": false, "filename": "ListXDSLServices.php", "line": "57"}, "connection": "nourtel", "explain": null, "start_percent": 94.448, "width_percent": 1.102}, {"sql": "select [equipe_id] from [equipe_responsable] where [user_id] = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 81}, {"index": 14, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.69556, "duration": 0.01669, "duration_str": "16.69ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:81", "source": {"index": 13, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=81", "ajax": false, "filename": "ListXDSLServices.php", "line": "81"}, "connection": "nourtel", "explain": null, "start_percent": 95.55, "width_percent": 1.042}, {"sql": "select count(*) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3')", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.713765, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:97", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=97", "ajax": false, "filename": "ListXDSLServices.php", "line": "97"}, "connection": "nourtel", "explain": null, "start_percent": 96.592, "width_percent": 1.079}, {"sql": "select count(distinct [num_ligne]) as aggregate from [mesures] where [mesure_type] = 'xdsl' and [equipe_id] in ('1', '3') and [num_ligne] is not null", "type": "query", "params": [], "bindings": ["xdsl", "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, {"index": 17, "namespace": "view", "name": "filament-panels::components.page.index", "file": "D:\\Projects\\nourtel\\resources\\views/vendor/filament-panels/components/page/index.blade.php", "line": 59}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\Projects\\nourtel\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7316382, "duration": 0.01802, "duration_str": "18.02ms", "memory": 0, "memory_str": null, "filename": "ListXDSLServices.php:103", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/XDSLServiceResource/Pages/ListXDSLServices.php", "file": "D:\\Projects\\nourtel\\app\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FFilament%2FResources%2FXDSLServiceResource%2FPages%2FListXDSLServices.php&line=103", "ajax": false, "filename": "ListXDSLServices.php", "line": "103"}, "connection": "nourtel", "explain": null, "start_percent": 97.671, "width_percent": 1.126}, {"sql": "update [sessions] set [payload] = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUGFXeG9QTHIzeEF6R1ZScUM5czJ1bUFJNzQ1SEU2czYyOXRRMmZoMCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwczovL25vdXJ0ZWwudGVzdC94LWQtcy1sLXNlcnZpY2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRiaTdXcndPcDBGWXlSY0txMGpMOUp1QWpSWTFjeTdXUmNtWGJsSUh6RVB4TDZoLjZLZVpaVyI7fQ==', [last_activity] = **********, [user_id] = 14, [ip_address] = '127.0.0.1', [user_agent] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where [id] = 'L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUGFXeG9QTHIzeEF6R1ZScUM5czJ1bUFJNzQ1SEU2czYyOXRRMmZoMCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwczovL25vdXJ0ZWwudGVzdC94LWQtcy1sLXNlcnZpY2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRiaTdXcndPcDBGWXlSY0txMGpMOUp1QWpSWTFjeTdXUmNtWGJsSUh6RVB4TDZoLjZLZVpaVyI7fQ==", **********, 14, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.853784, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "nourtel", "explain": null, "start_percent": 98.796, "width_percent": 1.204}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Mesure": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}, "App\\Models\\Equipe": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FEquipe.php&line=1", "ajax": false, "filename": "Equipe.php", "line": "?"}}, "App\\Models\\Region": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FRegion.php&line=1", "ajax": false, "filename": "Region.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services #2WNoSIfff4zVQdSfFDKx": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"date_range\" => array:2 [\n        \"created_from\" => null\n        \"created_until\" => null\n      ]\n      \"num_ligne_filter\" => array:1 [\n        \"num_ligne\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.x-d-s-l-service-resource.pages.list-x-d-s-l-services\"\n  \"component\" => \"App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices\"\n  \"id\" => \"2WNoSIfff4zVQdSfFDKx\"\n]", "filament.livewire.notifications #kGrdRgxq4SUd1UbKSVwO": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2673\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"kGrdRgxq4SUd1UbKSVwO\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 58, "messages": [{"message": "[\n  ability => reorder_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-930499021 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">reorder_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930499021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.067949, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1054131267 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054131267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.06852, "xdebug_link": null}, {"message": "[\n  ability => delete_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1692830411 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">delete_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692830411\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.127152, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1411001327 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411001327\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.127269, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-883254106 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883254106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.282054, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=208),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-304259546 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=208)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=208)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304259546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.28244, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1706230128 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706230128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.457773, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=189),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-849318437 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=189)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=189)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849318437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.457907, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1111728001 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111728001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.567493, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=188),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1692333164 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=188)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=188)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692333164\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.567618, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-831460155 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831460155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.737558, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=180),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2033512036 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=180)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=180)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033512036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.737703, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1747924819 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747924819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.870861, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=179),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-596442948 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=179)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=179)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596442948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.871072, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1253208082 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253208082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.987822, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=178),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1014765151 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=178)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=178)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014765151\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.987968, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2065613582 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065613582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.099466, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=177),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1457809843 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=177)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=177)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457809843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.099619, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1318141436 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318141436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.204679, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=174),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-717532915 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=174)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=174)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-717532915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.20481, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-401615297 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401615297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.361098, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=173),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-759116807 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=173)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=173)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759116807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.361216, "xdebug_link": null}, {"message": "[\n  ability => update_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059307860 data-indent-pad=\"  \"><span class=sf-dump-note>update_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059307860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.517059, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Mesure(id=172),\n  result => false,\n  user => 14,\n  arguments => [0 => Object(App\\Models\\Mesure)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-633102772 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Mesure(id=172)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\Mesure(id=172)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Mesure)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633102772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.517281, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2102960621 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102960621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.771668, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1240754468 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240754468\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.773263, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-893504728 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893504728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.773355, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-34981873 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34981873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.775619, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989010166 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989010166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.775746, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466863492 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466863492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.776888, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-428575846 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428575846\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.776978, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2086246356 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086246356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.777876, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-893911670 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893911670\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.777963, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1137683659 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137683659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.778817, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1783411548 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783411548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.778908, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-54434046 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54434046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.779629, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 14,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-674319186 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674319186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.779714, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1571542260 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571542260\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.781169, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-877436265 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877436265\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.781472, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-578764005 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578764005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.785899, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 14,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-504965475 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504965475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.785997, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310317081 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310317081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.811402, "xdebug_link": null}, {"message": "[\n  ability => view_any_configuration,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-103473099 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_configuration </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_configuration</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103473099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.814413, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Configuration,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Configuration]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1188700848 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Configuration</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Configuration</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\Configuration]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188700848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.814515, "xdebug_link": null}, {"message": "[\n  ability => view_any_emplacement,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-739254491 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_emplacement </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_emplacement</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739254491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.815513, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Emplacement,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Emplacement]\n]", "message_html": "<pre class=sf-dump id=sf-dump-453774955 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Emplacement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Emplacement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Emplacement]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453774955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.815645, "xdebug_link": null}, {"message": "[\n  ability => view_any_equipe,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944305670 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_equipe </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_equipe</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944305670\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.816804, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Equipe,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Equipe]\n]", "message_html": "<pre class=sf-dump id=sf-dump-990090816 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Equipe</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Equipe</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Equipe]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990090816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.816974, "xdebug_link": null}, {"message": "[\n  ability => view_any_mesure,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1708145453 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_mesure </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_mesure</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708145453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.818174, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Mesure,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Mesure]\n]", "message_html": "<pre class=sf-dump id=sf-dump-412320248 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Mesure</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Mesure</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Mesure]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412320248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.81838, "xdebug_link": null}, {"message": "[\n  ability => view_any_region,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-473208155 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_region </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_region</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473208155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.818918, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Region,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\Region]\n]", "message_html": "<pre class=sf-dump id=sf-dump-491409900 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Region</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Region</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Region]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491409900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.819004, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-278021162 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278021162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.819486, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 14,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-114927154 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114927154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.819571, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-967720676 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967720676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.820088, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 14,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-146917221 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146917221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.820175, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1689920904 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689920904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.820777, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 14,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1429162781 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429162781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.820858, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices", "uri": "GET x-d-s-l-services", "controller": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices@render<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/x-d-s-l-services", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor", "duration": "3.15s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ilo4ODNkZVQ0OEp0aXJSdDBhR2VSZEE9PSIsInZhbHVlIjoiVTMwaHUyQTYvYVo2S2xhSlh4KzVnalFPdk8wcHlOQ094cUhYRE4rQ1ZVS1JNVFJyL1FaK2dTZFg1QldVdkkxZ0xON1ZNV2c3UDVOWGc4MmN6YVVsVjVQOFVNQnV1L0VpYXUyTW05aG13OTNocTM1VlhTcnhnTVpXVzBQQWp3blE5elJ2VmlmSUlncDFiWFdBVGNsMEIzVzIvMnRHQjRZcTJkUkpPejJiQVRBSzd6NW5oUlF1NEdjWXF6aU5wSnJHdE8vbll0UHVOWWZ5VVYrOWsydjh1NHNyd0w0UURJdFdMSDduTmhML0orYz0iLCJtYWMiOiI2YWNjMjQ4ZGNmMDRmNjA2ZjQ2YTY2NzkwY2VjYjcyOGE5NWM0ODQ4ZTI4MDJlOWE0ZjQwY2YwMzU5NmY1MjM4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlB5RkxkOHpPUlQ4Y1BTMk04T0ZiM3c9PSIsInZhbHVlIjoiWllFTUpUQmo4QmVTNTJqZitVSnVHdi9RQTBYZDF0Vmw3WFhZTmRudEJuZ2FRQ2J5Mnh5MnpzTXpjaGoxdlZXMXk2MDR1eUttVWU3RTNHTGlLaXZPcWd0YVJ5UGdhUXN1NnNlT1NwMlNQZG5pczc5bXdRZHNpTlZUZWVFSStwdEwiLCJtYWMiOiJiODg5NDVjNzc2OWRlMTBhMzMyY2YxYzNjZDc4NTQ0ZmJmZDJkZTI1N2Q1OTg1ZmM2MmYxZjlhZDZkOWEzNTQyIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6ImU1NW9VejRVaHV4U0J3OHZvZFdoenc9PSIsInZhbHVlIjoiSXpBWmNHNEhmQXA3QkR5SFNuSmdLQ25aTDEwbGF3dEZOclQxWlN4bGpNd2NPNForMXFuM2NiRFdKOHo2TkFRUFB6UHJWK2dneElMYWxtTWZvTnhKdzMzRDFxK3FUbVVzUkZKeFZxMGdHbmNoTzBOUHIwMG1WSXo0Vkk0Z0NKRUsiLCJtYWMiOiIxNTU3MjEwM2NjYjY1YjA5MzgwZDM2NzRjZThhMWU0MDJkZTc1NWEwNmViM2JmMjZkNjU4NjUzYTYwMDhkOGIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://nourtel.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-763249725 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">14|yprycTiBDAKXrGkU9iUg8TnuMhdvQFVZtiTQDsiiQrLDFuhuuN4ha1TwoWdX|$2y$12$bi7WrwOp0FYyRcKq0jL9JuAjRY1cy7WRcmXblIHzEPxL6h.6KeZZW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PaWxoPLr3xAzGVRqC9s2umAI745HE6s629tQ2fh0</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L7FYKFOntqk6HDn7ha6BeGglsg56qGkzZHPb2xkz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763249725\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-726539012 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 12:29:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IkVCQVBCc1dPWWZGY0pjNDdheGNPQlE9PSIsInZhbHVlIjoiM0prVmJwZVlCQ3F5UWlnOVJWSEl6OFVqWEgzMW1nQjZnaVk1TDd1VU0zOWtQSCtWNmpRYlZMNHJpdTR5UDRBOFgrcDZEWnROQVROMnlreFVFeXovZXdURGlIVGIwQjZ0N3dlcXFxQTNBRXJOLzJuMWtEWkZyNkFBWGpXUS8yd20iLCJtYWMiOiI3MmUwYmY5MTM3MTcyNzljZjBhNDQ0ZjJmYTZhMThjMDJiNDE4ZTU3NGY2ODFmMDJlNDJlOTA4NWJhOTE5OThmIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 14:29:10 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"457 characters\">line_analyzer_session=eyJpdiI6Ikc1bUQ1c2JjWk5ta2kvTVdCNldhK2c9PSIsInZhbHVlIjoienlsc09LWmxlUmdOSGRKckZZWE56d2pjT1plMkNJeTNTNXdoZll6WmxwejN5MCttR0U0cHhPUEV0eEVYQUlXdnNkc1NUUHN3bmpwamd0VWF1c3VtdGlpcDk0K3B0UWp5RWIxZ2t6cXNLc2F4eHNJeHF1czFCb25SWDhQWjRiVHEiLCJtYWMiOiI3NjI1OTQyMzM4YWE3YmM2ZjllY2M4MTE0M2Q5MWU1ZTc3MWY1MDJlNjg3NmQwZmE5Mjg4Yzc0MDg1MWM2OTY2IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 14:29:10 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IkVCQVBCc1dPWWZGY0pjNDdheGNPQlE9PSIsInZhbHVlIjoiM0prVmJwZVlCQ3F5UWlnOVJWSEl6OFVqWEgzMW1nQjZnaVk1TDd1VU0zOWtQSCtWNmpRYlZMNHJpdTR5UDRBOFgrcDZEWnROQVROMnlreFVFeXovZXdURGlIVGIwQjZ0N3dlcXFxQTNBRXJOLzJuMWtEWkZyNkFBWGpXUS8yd20iLCJtYWMiOiI3MmUwYmY5MTM3MTcyNzljZjBhNDQ0ZjJmYTZhMThjMDJiNDE4ZTU3NGY2ODFmMDJlNDJlOTA4NWJhOTE5OThmIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 14:29:10 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"429 characters\">line_analyzer_session=eyJpdiI6Ikc1bUQ1c2JjWk5ta2kvTVdCNldhK2c9PSIsInZhbHVlIjoienlsc09LWmxlUmdOSGRKckZZWE56d2pjT1plMkNJeTNTNXdoZll6WmxwejN5MCttR0U0cHhPUEV0eEVYQUlXdnNkc1NUUHN3bmpwamd0VWF1c3VtdGlpcDk0K3B0UWp5RWIxZ2t6cXNLc2F4eHNJeHF1czFCb25SWDhQWjRiVHEiLCJtYWMiOiI3NjI1OTQyMzM4YWE3YmM2ZjllY2M4MTE0M2Q5MWU1ZTc3MWY1MDJlNjg3NmQwZmE5Mjg4Yzc0MDg1MWM2OTY2IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 14:29:10 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726539012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1088685345 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PaWxoPLr3xAzGVRqC9s2umAI745HE6s629tQ2fh0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://nourtel.test/x-d-s-l-services</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$bi7WrwOp0FYyRcKq0jL9JuAjRY1cy7WRcmXblIHzEPxL6h.6KeZZW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088685345\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/x-d-s-l-services", "action_name": "filament.admin.resources.x-d-s-l-services.index", "controller_action": "App\\Filament\\Resources\\XDSLServiceResource\\Pages\\ListXDSLServices"}, "badge": null}}