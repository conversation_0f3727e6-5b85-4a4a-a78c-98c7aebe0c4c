@php
use App\Models\Mesure;
use App\Models\Equipe;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

$dateParam = request('date');
$today = $dateParam ? Carbon::parse($dateParam) : Carbon::today();
$user = Auth::user();
$selectedEquipe = request('equipe_id');

// Récupérer les équipes dont l'utilisateur est responsable
$equipeIds = DB::table('equipe_responsable')
    ->where('user_id', $user->id)
    ->pluck('equipe_id');

// Afficher des informations de débogage
$debug = [
    'user_id' => $user->id,
    'equipe_ids' => $equipeIds,
    'count' => count($equipeIds)
];

$equipes = Equipe::whereIn('id', $equipeIds)
    ->orderBy('nom')
    ->get();

// Initialiser les variables avec des valeurs par défaut
$xdslToday = 0;
$xdslAvg = 0;
$xdslMax = 0;
$gponToday = 0;
$gponAvg = 0;
$gponMax = 0;
$userStats = collect();
$agentPerformance = collect();
$hasData = false;

// Vérifier que le chef d'équipe a accès à l'équipe sélectionnée
$hasAccess = false;
if ($selectedEquipe) {
    $hasAccess = $equipeIds->contains($selectedEquipe);
}

// Si aucune équipe n'est sélectionnée ou si le chef n'a pas accès à l'équipe, ne pas charger de données
if ($selectedEquipe && $hasAccess) {
    $hasData = true;

    // Construire la requête de base
    $xdslQuery = Mesure::where('mesure_type', 'xdsl');
    $gponQuery = Mesure::where('mesure_type', 'gpon');

    // Appliquer le filtre d'équipe
    $xdslQuery->where('equipe_id', $selectedEquipe);
    $gponQuery->where('equipe_id', $selectedEquipe);

    // xDSL
    $xdslToday = (clone $xdslQuery)->whereDate('mesure_date', $today)
        ->selectRaw('COUNT(DISTINCT num_ligne) as total')
        ->value('total') ?? 0;
    $xdslAvg = (clone $xdslQuery)->whereMonth('mesure_date', $today->month)
        ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
        ->groupByRaw('CONVERT(DATE, mesure_date)')
        ->get()
        ->avg('total') ?? 0;
    $xdslMax = (clone $xdslQuery)->whereMonth('mesure_date', $today->month)
        ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
        ->groupByRaw('CONVERT(DATE, mesure_date)')
        ->orderByDesc('total')
        ->first()?->total ?? 0;

    // GPON
    $gponToday = (clone $gponQuery)->whereDate('mesure_date', $today)
        ->selectRaw('COUNT(DISTINCT num_ligne) as total')
        ->value('total') ?? 0;
    $gponAvg = (clone $gponQuery)->whereMonth('mesure_date', $today->month)
        ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
        ->groupByRaw('CONVERT(DATE, mesure_date)')
        ->get()
        ->avg('total') ?? 0;
    $gponMax = (clone $gponQuery)->whereMonth('mesure_date', $today->month)
        ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
        ->groupByRaw('CONVERT(DATE, mesure_date)')
        ->orderByDesc('total')
        ->first()?->total ?? 0;

    // Récupérer les performances des agents pour la date sélectionnée
    $baseQuery = Mesure::select('created_by')
        ->whereNotNull('created_by')
        ->whereDate('mesure_date', $today)
        ->where('equipe_id', $selectedEquipe);

    // Récupérer les statistiques par utilisateur pour la date sélectionnée (distinct num_ligne)
    $userStats = $baseQuery->groupBy('created_by')
        ->selectRaw('created_by,
                    COUNT(DISTINCT CASE WHEN mesure_type = \'xdsl\' THEN num_ligne END) as xdsl_count,
                    COUNT(DISTINCT CASE WHEN mesure_type = \'gpon\' THEN num_ligne END) as gpon_count')
        ->havingRaw('COUNT(DISTINCT CASE WHEN mesure_type = \'xdsl\' THEN num_ligne END) + COUNT(DISTINCT CASE WHEN mesure_type = \'gpon\' THEN num_ligne END) > 0')
        ->get();

    // Récupérer les informations des utilisateurs
    $userIds = $userStats->pluck('created_by')->toArray();
    $users = User::whereIn('id', $userIds)->get()->keyBy('id');

    // Combiner les données
    $agentPerformance = $userStats->map(function($stat) use ($users, $selectedEquipe, $today) {
        $user = $users[$stat->created_by] ?? null;
        return [
            'created_by' => $stat->created_by,
            'name' => $user ? $user->name : 'Utilisateur inconnu',
            'xdsl_count' => $stat->xdsl_count,
            'gpon_count' => $stat->gpon_count,
            'has_locations' => Mesure::where('created_by', $stat->created_by)
                ->whereDate('mesure_date', $today)
                ->whereNotNull('lat')
                ->whereNotNull('lng')
                ->where('equipe_id', $selectedEquipe)
                ->exists()
        ];
    })->sortByDesc(function($stat) {
        return $stat['xdsl_count'] + $stat['gpon_count'];
    });
}
@endphp

<div id="dashboard-chef-container" class="dashboard-chef-wrapper">
    <link rel="stylesheet" href="{{ asset('css/dashboard-cards.css') }}">

    <style>
        /* Styles globaux pour la page */
        body {
            background-color: #f3f4f6;
        }

        /* Animation de pulsation pour les icônes */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        /* Animation de brillance pour les cartes */
        @keyframes shine {
            0% {
                background-position: -100% 0;
            }

            100% {
                background-position: 200% 0;
            }
        }

        /* Effet de brillance sur les cartes */
        .dashboard-stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.2) 50%,
                    rgba(255, 255, 255, 0) 100%);
            background-size: 200% 100%;
            animation: shine 3s infinite;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1;
            pointer-events: none;
        }

        .dashboard-stat-card:hover::after {
            opacity: 1;
        }

        /* Animation pour les icônes */
        .dashboard-stat-icon svg {
            animation: pulse 2s infinite ease-in-out;
        }

        /* Style pour le bouton de filtre lors de la soumission */
        .filter-button.submitting {
            position: relative;
            overflow: hidden;
        }

        .filter-button.submitting::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.3) 50%,
                    rgba(255, 255, 255, 0) 100%);
            animation: shine 1.5s infinite;
        }

        /* Amélioration de l'espacement global */
        .dashboard-stats-container {
            padding: 0.5rem;
            margin-bottom: 2rem;
        }

        /* Effet de survol amélioré pour les sélecteurs */
        .filter-select:hover {
            box-shadow: 0 0 0 3px rgba(200, 50, 255, 0.1);
        }

        /* Styles pour le tableau de performance des agents */
        .performance-table-container {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-top: 2rem;
        }

        .performance-table {
            width: 100%;
            border-collapse: collapse;
        }

        .performance-table th {
            background: linear-gradient(45deg, #4299e1, #63b3ed);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 1rem;
            position: relative;
        }

        .performance-table th:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 25%;
            height: 50%;
            width: 1px;
            background-color: rgba(255, 255, 255, 0.3);
        }

        .performance-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            background-color: #edf2f7;
        }

        .performance-table tr:nth-child(even) td {
            background-color: #f8fafc;
        }

        .performance-table tr:hover td {
            background-color: #ebf4ff;
        }

        .location-button {
            background: linear-gradient(45deg, #3182ce, #4299e1);
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .location-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .location-button:active {
            transform: translateY(0);
        }

        .location-button svg {
            width: 1.25rem;
            height: 1.25rem;
        }

        /* Styles pour le modal */
        .location-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .location-modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 80%;
            max-width: 1000px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #718096;
            transition: color 0.3s;
        }

        .modal-close:hover {
            color: #e53e3e;
        }

        .modal-body {
            flex: 1;
            overflow: hidden;
        }

        #map-container {
            width: 100%;
            height: 500px;
        }

        /* Animation pour le tableau */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .performance-table-container {
            animation: fadeIn 0.5s ease-out forwards;
        }
    </style>

    <style>
        :root {
            --primary-gradient: linear-gradient(45deg, #001e8c 0, #c832ff);
            --filter-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --filter-border-radius: 1rem;
            --transition-speed: 0.3s;
        }

        .filter-container {
            background-color: white;
            border-radius: var(--filter-border-radius);
            box-shadow: var(--filter-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .filter-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--primary-gradient);
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            align-items: flex-end;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .filter-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #4b5563;
            transition: color var(--transition-speed);
        }

        .filter-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 2px solid #e5e7eb;
            background-color: #f9fafb;
            font-size: 1rem;
            transition: all var(--transition-speed);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem;
            padding-right: 2.5rem;
        }

        .filter-select:focus {
            outline: none;
            border-color: #c832ff;
            box-shadow: 0 0 0 3px rgba(200, 50, 255, 0.2);
        }

        .filter-select:hover {
            border-color: #c832ff;
        }

        .filter-button {
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all var(--transition-speed);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .filter-button:active {
            transform: translateY(0);
        }

        .filter-button::after {
            content: '';
            display: inline-block;
            width: 1.25rem;
            height: 1.25rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z'%3E%3C/path%3E%3C/svg%3E");
            background-size: contain;
            margin-left: 0.5rem;
        }

        @media (max-width: 768px) {
            .filter-form {
                flex-direction: column;
            }

            .filter-button {
                width: 100%;
            }
        }
    </style>

    <div class="filter-container">
        <form action="" method="GET" class="filter-form" id="dashboard-filter-form">
            <div class="filter-group">
                <label for="equipe_id" class="filter-label">Équipe</label>
                <select name="equipe_id" id="equipe_id" class="filter-select">
                    @foreach($equipes as $equipe)
                    <option value="{{ $equipe->id }}" {{ $selectedEquipe == $equipe->id ? 'selected' : '' }}>
                        {{ $equipe->nom }}
                    </option>
                    @endforeach
                </select>
            </div>

            <div class="filter-group">
                <label for="date" class="filter-label">Date</label>
                <input type="date" name="date" id="date" class="filter-select" value="{{ $dateParam ?? $today->format('Y-m-d') }}">
            </div>

            <button type="submit" class="filter-button">Filtrer</button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterForm = document.getElementById('dashboard-filter-form');
            const filterButton = filterForm.querySelector('.filter-button');

            // Ajouter une animation au bouton lors de la soumission
            filterForm.addEventListener('submit', function(e) {
                filterButton.innerHTML = 'Chargement...';
                filterButton.style.width = filterButton.offsetWidth + 'px';
                filterButton.classList.add('submitting');

                // Ajouter une classe pour l'animation de chargement
                document.querySelectorAll('.dashboard-stat-card').forEach(card => {
                    card.style.opacity = '0.7';
                    card.style.transform = 'scale(0.98)';
                });
            });

            // Animer les cartes de statistiques au chargement
            const statCards = document.querySelectorAll('.dashboard-stat-card');
            statCards.forEach((card, index) => {
                // Initialiser les styles
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                // Animation d'entrée avec délai progressif
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';

                    // Animer les éléments internes
                    const content = card.querySelector('.dashboard-stat-content');
                    const icon = card.querySelector('.dashboard-stat-icon');

                    if (content) {
                        content.style.opacity = '0';
                        content.style.transform = 'translateX(-10px)';
                        setTimeout(() => {
                            content.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                            content.style.opacity = '1';
                            content.style.transform = 'translateX(0)';
                        }, 200);
                    }

                    if (icon) {
                        icon.style.opacity = '0';
                        icon.style.transform = 'translateX(10px)';
                        setTimeout(() => {
                            icon.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                            icon.style.opacity = '1';
                            icon.style.transform = 'translateX(0)';
                        }, 300);
                    }
                }, 100 + index * 100);
            });
        });
    </script>

    @if(!$hasData)
    <div class="no-data-message">
        <div class="no-data-container">
            @if($selectedEquipe && !$hasAccess)
            <svg xmlns="http://www.w3.org/2000/svg" class="no-data-icon error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div class="no-data-text">
                <h3>Accès non autorisé</h3>
                <p>Vous n'avez pas accès à cette équipe. Veuillez sélectionner une équipe dont vous êtes responsable.</p>
            </div>
            @else
            <svg xmlns="http://www.w3.org/2000/svg" class="no-data-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="no-data-text">
                <h3>Aucune donnée disponible</h3>
                <p>Veuillez sélectionner une équipe pour afficher les statistiques.</p>
            </div>
            @endif
        </div>
    </div>

    <style>
        .no-data-message {
            margin-top: 2rem;
            padding: 3rem;
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .no-data-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .no-data-icon {
            width: 4rem;
            height: 4rem;
            color: #a0aec0;
            margin-bottom: 1.5rem;
        }

        .no-data-icon.error {
            color: #e53e3e;
        }

        .no-data-text h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .no-data-text p {
            font-size: 1rem;
            color: #718096;
        }
    </style>
    @else
    <div class="dashboard-stats-container">
        <!-- xDSL Cards -->
        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Aujourd'hui</div>
                <div class="dashboard-stat-value">{{ $xdslToday }}</div>
                <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
            </div>
            <div class="dashboard-stat-icon xdsl">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        </div>

        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Moyen Journalier</div>
                <div class="dashboard-stat-value">{{ round($xdslAvg, 2) }}</div>
                <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
            </div>
            <div class="dashboard-stat-icon xdsl">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            </div>
        </div>

        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Nombre Maximum</div>
                <div class="dashboard-stat-value">{{ $xdslMax }}</div>
                <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
            </div>
            <div class="dashboard-stat-icon xdsl">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
            </div>
        </div>

        <!-- GPON Cards -->
        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Aujourd'hui</div>
                <div class="dashboard-stat-value">{{ $gponToday }}</div>
                <div class="dashboard-stat-type gpon">Lignes GPON</div>
            </div>
            <div class="dashboard-stat-icon gpon">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        </div>

        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Moyen Journalier</div>
                <div class="dashboard-stat-value">{{ round($gponAvg, 2) }}</div>
                <div class="dashboard-stat-type gpon">Lignes GPON</div>
            </div>
            <div class="dashboard-stat-icon gpon">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            </div>
        </div>

        <div class="dashboard-stat-card">
            <div class="dashboard-stat-content">
                <div class="dashboard-stat-label">Nombre Maximum</div>
                <div class="dashboard-stat-value">{{ $gponMax }}</div>
                <div class="dashboard-stat-type gpon">Lignes GPON</div>
            </div>
            <div class="dashboard-stat-icon gpon">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
            </div>
        </div>
    </div>

    <!-- Tableau des performances des agents -->
    <div class="performance-table-container">
        <table class="performance-table">
            <thead>
                <tr>
                    <th>Agent</th>
                    <th>xDSL</th>
                    <th>GPON</th>
                    <th>Total</th>
                    <th>Localisation</th>
                </tr>
            </thead>
            <tbody>
                @foreach($agentPerformance as $agent)
                <tr>
                    <td>{{ $agent['name'] }}</td>
                    <td>{{ $agent['xdsl_count'] }}</td>
                    <td>{{ $agent['gpon_count'] }}</td>
                    <td>{{ $agent['xdsl_count'] + $agent['gpon_count'] }}</td>
                    <td>
                        @if($agent['has_locations'])
                        <button type="button" class="location-button" data-user-id="{{ $agent['created_by'] }}" data-user-name="{{ $agent['name'] }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Voir
                        </button>
                        @else
                        <span class="text-gray-400">Non disponible</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Modal pour afficher les localisations -->
    <div id="location-modal" class="location-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">Localisations de <span id="modal-user-name"></span></div>
                <button type="button" class="modal-close" onclick="closeLocationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="map-container"></div>
            </div>
        </div>
    </div>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

    @push('scripts')
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    @endpush

    <style>
        /* Custom styles for map popups */
        .leaflet-popup {
            margin-bottom: 10px;
        }

        .leaflet-popup-content-wrapper {
            padding: 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            max-width: 120px;
        }

        .leaflet-popup-content {
            margin: 6px 8px;
            font-size: 11px;
            line-height: 1.2;
        }

        .leaflet-popup-tip {
            width: 8px;
            height: 8px;
        }

        .map-popup-content {
            text-align: center;
        }

        .map-popup-content .num-ligne {
            font-weight: bold;
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .map-popup-content .order-number {
            display: inline-block;
            background-color: #3182ce;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            font-size: 10px;
            margin-right: 3px;
        }

        .map-popup-content .date-time {
            font-size: 10px;
            color: #666;
        }

        .map-popup-content .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 3px;
            vertical-align: middle;
        }
    </style>

    @push('scripts')
    <script>
        let map;
        let markers = [];
        let activePopup = null;

        function openLocationModal(userId, userName) {
            // Afficher le modal
            document.getElementById('location-modal').classList.add('active');
            document.getElementById('modal-user-name').textContent = userName;

            // Initialiser la carte si elle n'existe pas encore
            if (!map) {
                map = L.map('map-container').setView([0, 0], 2);

                // Utiliser le tile server Google Maps comme spécifié dans les mémoires
                L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                    attribution: '© Google Maps',
                    maxZoom: 20
                }).addTo(map);
            }

            // Nettoyer les marqueurs existants
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];

            // Récupérer les valeurs des filtres actuels
            const equipeId = document.getElementById('equipe_id')?.value || '';
            const selectedDate = document.getElementById('date')?.value || '';

            // Construire l'URL avec les paramètres de filtre
            const url = new URL(`/api/users/${userId}/locations`, window.location.origin);
            if (equipeId) url.searchParams.append('equipe_id', equipeId);
            if (selectedDate) url.searchParams.append('date', selectedDate);

            // Récupérer les localisations de l'utilisateur avec les filtres
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        // Aucune localisation trouvée
                        return;
                    }

                    const bounds = L.latLngBounds();

                    // Trier les données par ID
                    data.sort((a, b) => a.id - b.id);

                    data.forEach((location, index) => {
                        if (location.lat && location.lng) {
                            // Numéro d'ordre (1-based)
                            const orderNumber = index + 1;

                            // Formater la date et l'heure
                            let dateTime = 'N/A';
                            if (location.created_at) {
                                const date = new Date(location.created_at);
                                const year = date.getFullYear();
                                const month = String(date.getMonth() + 1).padStart(2, '0');
                                const day = String(date.getDate()).padStart(2, '0');
                                const hours = String(date.getHours()).padStart(2, '0');
                                const minutes = String(date.getMinutes()).padStart(2, '0');

                                dateTime = `${year}-${month}-${day} / ${hours}:${minutes}`;
                            }

                            // Créer un popup compact
                            const popupContent = `
                            <div class="map-popup-content">
                                <div class="num-ligne">${location.num_ligne || 'N/A'}</div>
                                <div>
                                    <span class="status-dot" style="background-color: ${location.mesure_status === '1' ? 'green' : 'red'}"></span>
                                    <span class="date-time">${dateTime}</span>
                                </div>
                            </div>
                        `;

                            // Créer une icône personnalisée avec le numéro d'ordre
                            const customIcon = L.divIcon({
                                className: 'custom-div-icon',
                                html: `<div style="background-color: #3182ce; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; justify-content: center; align-items: center; font-weight: bold; box-shadow: 0 2px 5px rgba(0,0,0,0.3);">${orderNumber}</div>`,
                                iconSize: [24, 24],
                                iconAnchor: [12, 12]
                            });

                            // Créer le marqueur avec l'icône personnalisée
                            const marker = L.marker([location.lat, location.lng], {
                                    icon: customIcon
                                })
                                .addTo(map);

                            // Ajouter le popup mais ne pas l'ouvrir automatiquement
                            marker.bindPopup(popupContent);

                            // Ouvrir le popup au survol
                            marker.on('mouseover', function() {
                                this.openPopup();
                            });

                            // Fermer le popup quand la souris quitte le marqueur
                            marker.on('mouseout', function() {
                                // Ne pas fermer si c'est le popup actif
                                if (activePopup !== this) {
                                    this.closePopup();
                                }
                            });

                            // Gérer le clic pour maintenir le popup ouvert
                            marker.on('click', function() {
                                // Si un autre popup était actif, le fermer
                                if (activePopup && activePopup !== this) {
                                    activePopup.closePopup();
                                }

                                // Définir ce marqueur comme actif
                                activePopup = this;
                                this.openPopup();
                            });

                            markers.push(marker);
                            bounds.extend([location.lat, location.lng]);
                        }
                    });

                    // Ajuster la vue de la carte pour montrer tous les marqueurs
                    if (bounds.isValid()) {
                        map.fitBounds(bounds, {
                            padding: [50, 50]
                        });
                    }

                    // Ouvrir le premier popup pour donner un indice visuel à l'utilisateur
                    if (markers.length > 0) {
                        setTimeout(() => {
                            markers[0].openPopup();
                            activePopup = markers[0];
                        }, 500);
                    }
                })
                .catch(error => {
                    console.error('Erreur lors de la récupération des localisations:', error);
                });

            // Redimensionner la carte après l'ouverture du modal
            setTimeout(() => {
                map.invalidateSize();
            }, 100);

            // Fermer tous les popups quand on clique sur la carte
            map.on('click', function() {
                if (activePopup) {
                    activePopup.closePopup();
                    activePopup = null;
                }
            });
        }

        function closeLocationModal() {
            document.getElementById('location-modal').classList.remove('active');
            // Réinitialiser le popup actif
            activePopup = null;
        }

        // Fermer le modal si l'utilisateur clique en dehors du contenu
        document.getElementById('location-modal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeLocationModal();
            }
        });

        // Ajouter des écouteurs d'événements pour les boutons de localisation
        document.addEventListener('DOMContentLoaded', function() {
            const locationButtons = document.querySelectorAll('.location-button');
            locationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const userId = this.getAttribute('data-user-id');
                    const userName = this.getAttribute('data-user-name');
                    openLocationModal(userId, userName);
                });
            });
        });
    </script>
    @endpush
