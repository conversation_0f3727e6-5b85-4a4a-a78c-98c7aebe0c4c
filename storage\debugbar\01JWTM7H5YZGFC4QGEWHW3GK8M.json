{"__meta": {"id": "01JWTM7H5YZGFC4QGEWHW3GK8M", "datetime": "2025-06-03 10:08:43", "utime": **********.199753, "method": "GET", "uri": "/api/users/28/locations?equipe_id=9&date=2025-05-25", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.15433, "end": **********.199793, "duration": 1.0454630851745605, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": **********.15433, "relative_start": 0, "end": **********.898815, "relative_end": **********.898815, "duration": 0.****************, "duration_str": "744ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.898896, "relative_start": 0.****************, "end": **********.199797, "relative_end": 3.814697265625e-06, "duration": 0.***************, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.149612, "relative_start": 0.****************, "end": **********.155371, "relative_end": **********.155371, "duration": 0.005759000778198242, "duration_str": "5.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.196825, "relative_start": 1.****************, "end": **********.197299, "relative_end": **********.197299, "duration": 0.00047397613525390625, "duration_str": "474μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.19733, "relative_start": 1.****************, "end": **********.197353, "relative_end": **********.197353, "duration": 2.288818359375e-05, "duration_str": "23μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02444, "accumulated_duration_str": "24.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select [id], [num_ligne], [emplacement], [lat], [lng], [mesure_status], [mesure_date], [created_at] from [mesures] where [created_by] = '28' and ([lat] != '0' or [lng] != '0') and [equipe_id] = '9' and cast([mesure_date] as date) = '2025-05-25' order by [id] asc", "type": "query", "params": [], "bindings": ["28", "0", "0", "9", "2025-05-25"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.159486, "duration": 0.02444, "duration_str": "24.44ms", "memory": 0, "memory_str": null, "filename": "UserLocationController.php:42", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=42", "ajax": false, "filename": "UserLocationController.php", "line": "42"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mesure": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/28/locations?date=2025-05-25&equipe_id=9", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations", "uri": "GET api/users/{userId}/locations", "controller": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/UserLocationController.php:18-45</a>", "middleware": "api", "duration": "1.05s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1850838662 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>equipe_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-25</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850838662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1308525891 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1308525891\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-890560346 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw3UmxELzRFWVEzdEpna1JubkIzRUE9PSIsInZhbHVlIjoiMmQ2WHozUDJUUkN5enFuWEZ3WDZFQ0doc2dGc3psaE93NE1Nckg3ODc4N0ZUK00yYzRtcUg1QldvTGpBWktUTUIzRWZaNGR2aHdKOHJRVVlFV214cnUwamYvMVFMK0ZaNmNVTXdJRWtRUkNsUlZOSWdzdWZtRjZxWFZONlRxbEEiLCJtYWMiOiI1NWM3YTA2MDIzNGUyZjRiN2QxYWU2MTg2OTEwMzI0YmIwMzAwMTY3ZTg4M2E1Y2E2OWIzYWFjYTUzZGM1YTVhIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6IlNiK0pkcmdhMUlFQTk2Z1ZLY2lna3c9PSIsInZhbHVlIjoiUU16eXVTdXprcFJoTGVVZUprUDJ2K09MMzBpSXd1NmJXa05QalZXRURlQVZycXFiVzE3WTRDZ1crcU5DTFMrWnZUYWcyRmo5VFBBMUk2enI5T2FPUll5NnQzV3BhaXFKL2VhMHFiQzRlUnIvdmszZnQ0R1JTbCtsQnM4em90MTEiLCJtYWMiOiIwNmU5N2EwYzBkNDI2OTAyMzc5YjNhMDFhYmI5M2M4Yzk2ZjI2NTM2MTJmM2IxYWJhNGNlZDYxZmFlMTk4YWFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?region_id=&amp;equipe_id=9&amp;date=2025-05-25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890560346\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-391675178 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imw3UmxELzRFWVEzdEpna1JubkIzRUE9PSIsInZhbHVlIjoiMmQ2WHozUDJUUkN5enFuWEZ3WDZFQ0doc2dGc3psaE93NE1Nckg3ODc4N0ZUK00yYzRtcUg1QldvTGpBWktUTUIzRWZaNGR2aHdKOHJRVVlFV214cnUwamYvMVFMK0ZaNmNVTXdJRWtRUkNsUlZOSWdzdWZtRjZxWFZONlRxbEEiLCJtYWMiOiI1NWM3YTA2MDIzNGUyZjRiN2QxYWU2MTg2OTEwMzI0YmIwMzAwMTY3ZTg4M2E1Y2E2OWIzYWFjYTUzZGM1YTVhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlNiK0pkcmdhMUlFQTk2Z1ZLY2lna3c9PSIsInZhbHVlIjoiUU16eXVTdXprcFJoTGVVZUprUDJ2K09MMzBpSXd1NmJXa05QalZXRURlQVZycXFiVzE3WTRDZ1crcU5DTFMrWnZUYWcyRmo5VFBBMUk2enI5T2FPUll5NnQzV3BhaXFKL2VhMHFiQzRlUnIvdmszZnQ0R1JTbCtsQnM4em90MTEiLCJtYWMiOiIwNmU5N2EwYzBkNDI2OTAyMzc5YjNhMDFhYmI5M2M4Yzk2ZjI2NTM2MTJmM2IxYWJhNGNlZDYxZmFlMTk4YWFiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391675178\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-72899402 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:08:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72899402\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-499342030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-499342030\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/28/locations?date=2025-05-25&equipe_id=9", "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations"}, "badge": null}}