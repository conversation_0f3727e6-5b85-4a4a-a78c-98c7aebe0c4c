<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ConfigurationResource\Pages;
use App\Models\Configuration;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ConfigurationResource extends Resource
{
    protected static ?string $model = Configuration::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';

    protected static ?string $navigationLabel = 'Configurations';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 5;

    // Let Shield handle the permissions
    // No need to override shouldRegisterNavigation

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->label('Name'),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'GPON' => 'GPON',
                        'xDSL' => 'xDSL',
                    ])
                    ->unique(ignoreRecord: true)
                    ->label('Type'),
                Forms\Components\TextInput::make('login')
                    ->required()
                    ->maxLength(255)
                    ->label('Login'),
                Forms\Components\TextInput::make('password')
                    ->required()
                    ->maxLength(255)
                    ->label('Password'),
                Forms\Components\TextInput::make('passerelle')
                    ->required()
                    ->maxLength(255)
                    ->label('Passerelle'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Name'),
                Tables\Columns\TextColumn::make('type')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'GPON' => 'success',
                        'xDSL' => 'info',
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('login')
                    ->searchable()
                    ->sortable()
                    ->label('Login'),
                Tables\Columns\TextColumn::make('passerelle')
                    ->searchable()
                    ->sortable()
                    ->label('Passerelle'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Created At'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Updated At'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'GPON' => 'GPON',
                        'xDSL' => 'xDSL',
                    ])
                    ->label('Type'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConfigurations::route('/'),
            'create' => Pages\CreateConfiguration::route('/create'),
            'edit' => Pages\EditConfiguration::route('/{record}/edit'),
        ];
    }
}
