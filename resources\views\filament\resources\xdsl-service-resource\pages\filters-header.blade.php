<div class="flex flex-col gap-6 bg-white dark:bg-gray-900 p-6">
    <!-- Header with Title -->
    <div class="flex flex-col gap-6">
        <h1 class="text-2xl font-bold tracking-tight md:text-3xl">
            {{ __('Historique Qualifications xDSL') }}
        </h1>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Total Mesures Card -->
            <div class="shadow-lg rounded-lg overflow-hidden min-h-[120px] border border-indigo-400" style="background: linear-gradient(45deg, #001e8c 0, #c832ff); color: white;">
                <div class="flex justify-between items-center p-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="color: white;">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div class="text-right">
                        <h2 class="text-3xl font-bold" style="color: white;">{{ $totalMesures }}</h2>
                        <p class="text-sm mt-1" style="color: white; opacity: 0.9;">Total des mesures</p>
                    </div>
                </div>
            </div>

            <!-- Total Abonnés Card -->
            <div class="shadow-lg rounded-lg overflow-hidden min-h-[120px] border border-indigo-400" style="background: linear-gradient(45deg, #001e8c 0, #c832ff); color: white;">
                <div class="flex justify-between items-center p-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="color: white;">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div class="text-right">
                        <h2 class="text-3xl font-bold" style="color: white;">{{ $totalAbonnes }}</h2>
                        <p class="text-sm mt-1" style="color: white; opacity: 0.9;">Total des abonnés</p>
                    </div>
                </div>
            </div>

            <!-- Placeholder for additional cards if needed -->
            <div class="hidden lg:block"></div>
            <div class="hidden lg:block"></div>
        </div>

        <!-- Filter Bar -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <!-- Responsive Filter Layout -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <!-- Date Filters - Stack on mobile, inline on desktop -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-6">Du</span>
                        <div class="relative flex-1">
                            <input
                                type="date"
                                id="date_from"
                                wire:model="tableFilters.date_range.created_from"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                            >
                            <div wire:loading wire:target="tableFilters.date_range.created_from" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="animate-spin h-4 w-4 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-6">Au</span>
                        <div class="relative flex-1">
                            <input
                                type="date"
                                id="date_to"
                                wire:model="tableFilters.date_range.created_until"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                            >
                            <div wire:loading wire:target="tableFilters.date_range.created_until" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="animate-spin h-4 w-4 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap justify-center sm:justify-end gap-2">
                    <button
                        type="button"
                        wire:click="filter"
                        class="inline-flex items-center justify-center rounded-lg border border-primary-600 bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-primary-500 dark:hover:bg-primary-600 dark:focus:ring-offset-gray-800"
                    >
                        <span wire:loading.remove wire:target="filter">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                            </svg>
                            Filtrer
                        </span>
                        <span wire:loading wire:target="filter" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Filtration...
                        </span>
                    </button>

                    <button
                        type="button"
                        wire:click="resetTableFiltersForm"
                        class="inline-flex items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 dark:focus:ring-offset-gray-800"
                    >
                        <span wire:loading.remove wire:target="resetTableFiltersForm">Réinitialiser</span>
                        <span wire:loading wire:target="resetTableFiltersForm" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Réinitialisation...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div
        wire:loading
        class="animate-pulse flex items-center text-primary-600 dark:text-primary-500"
    >
        <svg class="animate-spin -ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm font-medium">Chargement...</span>
    </div>
</div>

<!-- Table Loading Overlay -->
<div
    wire:loading
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    style="backdrop-filter: blur(2px);"
>
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl flex flex-col items-center">
        <svg class="animate-spin h-10 w-10 text-primary-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg font-medium text-gray-900 dark:text-gray-100">Chargement des données...</span>
        <span class="text-sm text-gray-500 dark:text-gray-400 mt-2">Veuillez patienter pendant que nous traitons votre demande</span>
    </div>
</div>
