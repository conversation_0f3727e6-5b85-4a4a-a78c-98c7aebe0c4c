@if($getRecord()->lat)
    <button
        type="button"
        onclick="showLocationMap({{ $getRecord()->id }}, {{ $getRecord()->lat }}, {{ $getRecord()->lng }}, '{{ $getRecord()->emplacement }}', '{{ $getRecord()->num_ligne }}', {{ $getRecord()->g_rx_power ?? 'null' }}, {{ $getRecord()->g_tx_power ?? 'null' }}, {{ $getRecord()->g_voltage ?? 'null' }}, {{ $getRecord()->g_bias_current ?? 'null' }}, '{{ $getRecord()->g_pon_alarm_info ?? '' }}', '{{ $getRecord()->creator->name ?? '' }}', '{{ $getRecord()->equipe->nom ?? '' }}', '{{ $getRecord()->region->nom ?? '' }}', '{{ $getRecord()->mesure_status }}', '{{ $getRecord()->mesure_date ? \Carbon\Carbon::parse($getRecord()->mesure_date)->format('d/m/Y H:i') : '' }}')"
        class="filament-button filament-button-size-sm bg-primary-600 hover:bg-primary-500 text-white inline-flex items-center justify-center py-1 px-3 rounded-lg font-medium gap-1 shadow focus:outline-none focus:ring-offset-2 focus:ring-2 focus:ring-primary-500"
    >
        <x-heroicon-o-map-pin class="w-4 h-4" />
        <span>Voir localisation</span>
    </button>
@endif

