<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AssignNavigationPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:assign-navigation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign navigation permissions to roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting assignment of navigation permissions...');

        // Define navigation permissions
        $navigationPermissions = [
            'view_service_gpon',
            'view_consultation_ligne',
            'view_historique_qualification',
        ];

        // Make sure permissions exist
        foreach ($navigationPermissions as $permissionName) {
            Permission::firstOrCreate(['name' => $permissionName, 'guard_name' => 'web']);
        }

        // Get permissions
        $permissions = Permission::whereIn('name', $navigationPermissions)->get();

        // Assign permissions to roles
        $this->assignPermissionsToRole('super_admin', $permissions);
        $this->assignPermissionsToRole(2, $permissions); // admin
        $this->assignPermissionsToRole(3, $permissions); // chef d'équipe
        $this->assignPermissionsToRole(4, $permissions); // agent

        $this->info('Navigation permissions assigned successfully!');
        
        return Command::SUCCESS;
    }

    /**
     * Assign permissions to a role by name or ID
     */
    protected function assignPermissionsToRole($roleIdentifier, $permissions)
    {
        $role = is_numeric($roleIdentifier) 
            ? Role::find($roleIdentifier)
            : Role::where('name', $roleIdentifier)->first();

        if (!$role) {
            $this->warn("Role {$roleIdentifier} not found");
            return;
        }

        $role->givePermissionTo($permissions);
        $this->info("Assigned navigation permissions to role {$role->name}");
    }
}
