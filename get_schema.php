<?php
// <PERSON>ript to extract database schema from SQL Server using PDO

// Database connection parameters from .env file
$host = '************';
$port = '1444';
$database = 'nourtel';
$username = 'mhar<PERSON><PERSON><PERSON>';
$password = 'Karim1993';
$encrypt = 'yes';
$trustServerCertificate = 'true';

try {
    // PDO connection string for SQL Server
    $dsn = "sqlsrv:Server=$host,$port;Database=$database;TrustServerCertificate=1";
    $conn = new PDO($dsn, $username, $password);

    // Set error mode to exceptions
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected successfully to the database.\n";
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Output file
$outputFile = fopen("schema.sql", "w");

// Write header
fwrite($outputFile, "-- SQL Server Database Schema for '$database'\n");
fwrite($outputFile, "-- Generated on " . date("Y-m-d H:i:s") . "\n\n");

// Get all tables
$tableQuery = "
SELECT
    t.name AS table_name,
    SCHEMA_NAME(t.schema_id) AS schema_name
FROM
    sys.tables t
ORDER BY
    schema_name, table_name;
";

try {
    $tableResult = $conn->query($tableQuery);

    $tables = [];
    while ($row = $tableResult->fetch(PDO::FETCH_ASSOC)) {
        $tables[] = [
            'schema' => $row['schema_name'],
            'name' => $row['table_name']
        ];
    }
} catch (PDOException $e) {
    die("Error fetching tables: " . $e->getMessage());
}

// Process each table
foreach ($tables as $table) {
    $schemaName = $table['schema'];
    $tableName = $table['name'];
    $fullTableName = "[$schemaName].[$tableName]";

    echo "Processing table: $fullTableName\n";

    // Get table columns
    $columnQuery = "
    SELECT
        c.name AS column_name,
        t.name AS data_type,
        c.max_length,
        c.precision,
        c.scale,
        c.is_nullable,
        c.is_identity,
        OBJECT_DEFINITION(c.default_object_id) AS default_value
    FROM
        sys.columns c
    JOIN
        sys.types t ON c.user_type_id = t.user_type_id
    WHERE
        c.object_id = OBJECT_ID('$schemaName.$tableName')
    ORDER BY
        c.column_id;
    ";

    try {
        $columnResult = $conn->query($columnQuery);

        // Start CREATE TABLE statement
        fwrite($outputFile, "-- Table: $fullTableName\n");
        fwrite($outputFile, "CREATE TABLE $fullTableName (\n");

        $columns = [];
        while ($row = $columnResult->fetch(PDO::FETCH_ASSOC)) {
            $columnName = $row['column_name'];
            $dataType = $row['data_type'];
            $maxLength = $row['max_length'];
            $precision = $row['precision'];
            $scale = $row['scale'];
            $isNullable = $row['is_nullable'] ? 'NULL' : 'NOT NULL';
            $isIdentity = $row['is_identity'] ? 'IDENTITY(1,1)' : '';
            $defaultValue = $row['default_value'] ? "DEFAULT " . $row['default_value'] : '';

            // Format data type with precision/scale/length
            if (in_array($dataType, ['nvarchar', 'varchar', 'char', 'nchar'])) {
                if ($maxLength == -1) {
                    $dataType .= '(MAX)';
                } else {
                    $dataType .= '(' . ($dataType == 'nvarchar' || $dataType == 'nchar' ? $maxLength/2 : $maxLength) . ')';
                }
            } elseif (in_array($dataType, ['decimal', 'numeric'])) {
                $dataType .= "($precision, $scale)";
            }

            $columnDef = "    [$columnName] $dataType $isIdentity $isNullable $defaultValue";
            $columns[] = rtrim($columnDef);
        }
    } catch (PDOException $e) {
        die("Error fetching columns for $fullTableName: " . $e->getMessage());
    }

    // Get primary key
    $pkQuery = "
    SELECT
        i.name AS index_name,
        c.name AS column_name
    FROM
        sys.indexes i
    JOIN
        sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
    JOIN
        sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
    WHERE
        i.is_primary_key = 1 AND
        i.object_id = OBJECT_ID('$schemaName.$tableName')
    ORDER BY
        ic.key_ordinal;
    ";

    try {
        $pkResult = $conn->query($pkQuery);

        $pkColumns = [];
        $pkName = '';

        while ($row = $pkResult->fetch(PDO::FETCH_ASSOC)) {
            $pkName = $row['index_name'];
            $pkColumns[] = $row['column_name'];
        }

        if (!empty($pkColumns)) {
            $pkDef = "    CONSTRAINT [$pkName] PRIMARY KEY (" . implode(', ', array_map(function($col) { return "[$col]"; }, $pkColumns)) . ")";
            $columns[] = $pkDef;
        }

        // Write columns and constraints
        fwrite($outputFile, implode(",\n", $columns) . "\n);\n\n");
    } catch (PDOException $e) {
        die("Error fetching primary key for $fullTableName: " . $e->getMessage());
    }

    // Get foreign keys
    $fkQuery = "
    SELECT
        fk.name AS fk_name,
        COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS fk_column,
        OBJECT_NAME(fk.referenced_object_id) AS referenced_table,
        OBJECT_SCHEMA_NAME(fk.referenced_object_id) AS referenced_schema,
        COL_NAME(fk.referenced_object_id, fkc.referenced_column_id) AS referenced_column,
        fk.delete_referential_action,
        fk.update_referential_action
    FROM
        sys.foreign_keys fk
    JOIN
        sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
    WHERE
        fk.parent_object_id = OBJECT_ID('$schemaName.$tableName')
    ORDER BY
        fk.name, fkc.constraint_column_id;
    ";

    try {
        $fkResult = $conn->query($fkQuery);

        $fkConstraints = [];
        $currentFk = '';
        $fkColumns = [];
        $refColumns = [];
        $refTable = '';
        $refSchema = '';
        $deleteAction = 0;
        $updateAction = 0;

        while ($row = $fkResult->fetch(PDO::FETCH_ASSOC)) {
            if ($currentFk != $row['fk_name']) {
                if (!empty($currentFk)) {
                    // Write previous FK
                    $deleteActionText = getActionText($deleteAction);
                    $updateActionText = getActionText($updateAction);

                    $fkDef = "ALTER TABLE $fullTableName ADD CONSTRAINT [$currentFk] FOREIGN KEY (" .
                             implode(', ', array_map(function($col) { return "[$col]"; }, $fkColumns)) . ") " .
                             "REFERENCES [$refSchema].[$refTable] (" .
                             implode(', ', array_map(function($col) { return "[$col]"; }, $refColumns)) . ") " .
                             "ON DELETE $deleteActionText ON UPDATE $updateActionText;";

                    fwrite($outputFile, $fkDef . "\n");
                }

                // Start new FK
                $currentFk = $row['fk_name'];
                $fkColumns = [$row['fk_column']];
                $refColumns = [$row['referenced_column']];
                $refTable = $row['referenced_table'];
                $refSchema = $row['referenced_schema'];
                $deleteAction = $row['delete_referential_action'];
                $updateAction = $row['update_referential_action'];
            } else {
                // Add column to current FK
                $fkColumns[] = $row['fk_column'];
                $refColumns[] = $row['referenced_column'];
            }
        }

        // Write last FK if exists
        if (!empty($currentFk)) {
            $deleteActionText = getActionText($deleteAction);
            $updateActionText = getActionText($updateAction);

            $fkDef = "ALTER TABLE $fullTableName ADD CONSTRAINT [$currentFk] FOREIGN KEY (" .
                     implode(', ', array_map(function($col) { return "[$col]"; }, $fkColumns)) . ") " .
                     "REFERENCES [$refSchema].[$refTable] (" .
                     implode(', ', array_map(function($col) { return "[$col]"; }, $refColumns)) . ") " .
                     "ON DELETE $deleteActionText ON UPDATE $updateActionText;";

            fwrite($outputFile, $fkDef . "\n");
        }
    } catch (PDOException $e) {
        die("Error fetching foreign keys for $fullTableName: " . $e->getMessage());
    }

    // Get indexes (non-primary key)
    $indexQuery = "
    SELECT
        i.name AS index_name,
        i.is_unique,
        c.name AS column_name,
        ic.is_descending_key
    FROM
        sys.indexes i
    JOIN
        sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
    JOIN
        sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
    WHERE
        i.is_primary_key = 0 AND
        i.is_unique_constraint = 0 AND
        i.object_id = OBJECT_ID('$schemaName.$tableName')
    ORDER BY
        i.name, ic.key_ordinal;
    ";

    try {
        $indexResult = $conn->query($indexQuery);

        $indexes = [];
        $currentIndex = '';
        $isUnique = false;
        $indexColumns = [];

        while ($row = $indexResult->fetch(PDO::FETCH_ASSOC)) {
            if ($currentIndex != $row['index_name']) {
                if (!empty($currentIndex)) {
                    // Write previous index
                    $uniqueText = $isUnique ? 'UNIQUE ' : '';
                    $indexDef = "CREATE {$uniqueText}INDEX [$currentIndex] ON $fullTableName (" .
                               implode(', ', $indexColumns) . ");";

                    fwrite($outputFile, $indexDef . "\n");
                }

                // Start new index
                $currentIndex = $row['index_name'];
                $isUnique = $row['is_unique'];
                $direction = $row['is_descending_key'] ? 'DESC' : 'ASC';
                $indexColumns = ["[" . $row['column_name'] . "] $direction"];
            } else {
                // Add column to current index
                $direction = $row['is_descending_key'] ? 'DESC' : 'ASC';
                $indexColumns[] = "[" . $row['column_name'] . "] $direction";
            }
        }

        // Write last index if exists
        if (!empty($currentIndex)) {
            $uniqueText = $isUnique ? 'UNIQUE ' : '';
            $indexDef = "CREATE {$uniqueText}INDEX [$currentIndex] ON $fullTableName (" .
                       implode(', ', $indexColumns) . ");";

            fwrite($outputFile, $indexDef . "\n");
        }

        fwrite($outputFile, "\n");
    } catch (PDOException $e) {
        die("Error fetching indexes for $fullTableName: " . $e->getMessage());
    }
}

// Get all views
$viewQuery = "
SELECT
    v.name AS view_name,
    SCHEMA_NAME(v.schema_id) AS schema_name,
    OBJECT_DEFINITION(v.object_id) AS view_definition
FROM
    sys.views v
ORDER BY
    schema_name, view_name;
";

try {
    $viewResult = $conn->query($viewQuery);

    while ($row = $viewResult->fetch(PDO::FETCH_ASSOC)) {
        $schemaName = $row['schema_name'];
        $viewName = $row['view_name'];
        $viewDefinition = $row['view_definition'];

        fwrite($outputFile, "-- View: [$schemaName].[$viewName]\n");
        fwrite($outputFile, "CREATE VIEW [$schemaName].[$viewName] AS\n$viewDefinition\nGO\n\n");
    }
} catch (PDOException $e) {
    die("Error fetching views: " . $e->getMessage());
}

// Close file and connection
fclose($outputFile);
$conn = null; // Close PDO connection

echo "Schema extraction completed. Check schema.sql file.\n";

// Helper function to convert action code to text
function getActionText($action) {
    switch ($action) {
        case 0: return 'NO ACTION';
        case 1: return 'CASCADE';
        case 2: return 'SET NULL';
        case 3: return 'SET DEFAULT';
        default: return 'NO ACTION';
    }
}
?>
