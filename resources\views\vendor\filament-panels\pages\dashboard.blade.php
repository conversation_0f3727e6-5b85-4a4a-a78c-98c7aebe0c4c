@php
use App\Models\Mesure;
use Illuminate\Support\Carbon;
// Cette vue est la vue publiée du dashboard (vendor/filament-panels)
@endphp

<x-filament-panels::page class="fi-dashboard-page">
    @if (method_exists($this, 'filtersForm'))
    {{ $this->filtersForm }}
    @endif

    @auth
    @php
    $user = Auth::user();
    $roleId = DB::table('roles')
    ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
    ->where('model_has_roles.model_id', $user->id)
    ->where('model_has_roles.model_type', get_class($user))
    ->select('roles.id')
    ->first()->id ?? null;
    @endphp

    @if ($roleId == 2)
        @include('partials.dashboardadmin')
    @elseif ($roleId == 3)
        @include('partials.dashboardchef')
    @elseif ($roleId == 4)
        @include('partials.dashboardagent')
    @elseif ($roleId == 1)
        @include('partials.dashboardadmin')
    @endif
    @endauth
</x-filament-panels::page>