<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> 
                <?php echo e(__('Informations Personnelles')); ?>

             <?php $__env->endSlot(); ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?php echo e(__('Nom')); ?>

                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        <?php echo e($this->getUser()->name); ?>

                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?php echo e(__('Matricule')); ?>

                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        <?php echo e($this->getUser()->matricule ?? 'Non défini'); ?>

                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?php echo e(__('Email')); ?>

                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        <?php echo e($this->getUser()->email); ?>

                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?php echo e(__('Rôle')); ?>

                    </div>
                    <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                        <?php echo e($this->getUserRole()); ?>

                    </div>
                </div>

                <?php
                    $equipes = $this->getUserEquipes();
                ?>

                <!--[if BLOCK]><![endif]--><?php if(count($equipes['agent']) > 0): ?>
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            <?php echo e(__('Membre des équipes')); ?>

                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $equipes['agent']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $equipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($equipe->nom); ?><?php echo e($index < count($equipes['agent']) - 1 ? ', ' : ''); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if(count($equipes['responsable']) > 0): ?>
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            <?php echo e(__('Responsable des équipes')); ?>

                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $equipes['responsable']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $equipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($equipe->nom); ?><?php echo e($index < count($equipes['responsable']) - 1 ? ', ' : ''); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <?php
                    $regions = $this->getUserRegions();
                ?>

                <!--[if BLOCK]><![endif]--><?php if(count($regions) > 0): ?>
                    <div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            <?php echo e(__('Régions')); ?>

                        </div>
                        <div class="mt-1 text-gray-900 dark:text-white">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($region->nom); ?><?php echo e($index < count($regions) - 1 ? ', ' : ''); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <div class="mt-8">
            <?php echo e($this->form); ?>


            <div class="mt-4 flex justify-end">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['wire:click' => 'updatePassword','type' => 'submit']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'updatePassword','type' => 'submit']); ?>
                    <?php echo e(__('Mettre à jour')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\Projects\nourtel\resources\views/filament/pages/profile.blade.php ENDPATH**/ ?>