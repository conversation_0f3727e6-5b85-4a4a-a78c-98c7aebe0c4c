<?php

namespace App\Filament\Resources\ConsultationLigneXDSLResource\Pages;

use App\Filament\Resources\ConsultationLigneXDSLResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\View\View;

class ListConsultationLignesXDSL extends ListRecords
{
    protected static string $resource = ConsultationLigneXDSLResource::class;

    // Propriété pour suivre si une recherche a été effectuée
    public bool $hasSearched = false;

    protected static string $view = 'filament.resources.consultation-ligne-xdsl-resource.pages.list-consultation-lignes-xdsl';

    protected function getTableFiltersFormWidth(): string
    {
        return '4xl';
    }

    protected function getTableFiltersFormMaxHeight(): string
    {
        return '600px';
    }

    protected function hasSearchContainer(): bool
    {
        return false;
    }

    protected function hasFiltersDropdown(): bool
    {
        return false;
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    public function getHeader(): ?View
    {
        return view('filament.resources.consultation-ligne-xdsl-resource.pages.filters-header');
    }

    protected function hasBreadcrumbs(): bool
    {
        return false;
    }

    public function filter(): void
    {
        // Manually apply filters when the Filter button is clicked
        $this->resetPage();

        // Marquer qu'une recherche a été effectuée
        $this->hasSearched = true;

        // Force a table refresh
        $this->dispatch('refresh');
    }

    public function resetTableFiltersForm(): void
    {
        // Réinitialiser tous les filtres
        $this->tableFilters = [];

        // Réinitialiser la pagination
        $this->resetPage();

        // Réinitialiser l'état de recherche
        $this->hasSearched = false;

        // Forcer le rafraîchissement de la page
        $this->dispatch('refresh');
    }

    public function mount(): void
    {
        parent::mount();

        // Vérifier si des filtres sont déjà appliqués
        $numLigneFilter = request('tableFilters.num_ligne_filter.num_ligne');
        if (!empty($numLigneFilter)) {
            $this->hasSearched = true;
        }
    }
}
