<?php
use App\Models\Mesure;
use App\Models\Equipe;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

$dateParam = request('date');
$today = $dateParam ? Carbon::parse($dateParam) : Carbon::today();
$user = Auth::user();

// Récupérer l'équipe de l'agent
$equipeId = DB::table('equipe_agent')
    ->where('user_id', $user->id)
    ->value('equipe_id');

// Construire les requêtes de base pour l'agent
$agentXdslQuery = Mesure::where('mesure_type', 'xdsl')
    ->where('created_by', $user->id);
$agentGponQuery = Mesure::where('mesure_type', 'gpon')
    ->where('created_by', $user->id);

// Statistiques xDSL de l'agent
$agentXdslToday = (clone $agentXdslQuery)->whereDate('mesure_date', $today)
    ->selectRaw('COUNT(DISTINCT num_ligne) as total')
    ->value('total') ?? 0;
$agentXdslAvg = (clone $agentXdslQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->get()
    ->avg('total') ?? 0;

// Statistiques GPON de l'agent
$agentGponToday = (clone $agentGponQuery)->whereDate('mesure_date', $today)
    ->selectRaw('COUNT(DISTINCT num_ligne) as total')
    ->value('total') ?? 0;
$agentGponAvg = (clone $agentGponQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->get()
    ->avg('total') ?? 0;

// Statistiques de l'équipe aujourd'hui
$equipeXdslToday = 0;
$equipeGponToday = 0;

if ($equipeId) {
    $equipeXdslToday = Mesure::where('mesure_type', 'xdsl')
        ->where('equipe_id', $equipeId)
        ->whereDate('mesure_date', $today)
        ->selectRaw('COUNT(DISTINCT num_ligne) as total')
        ->value('total') ?? 0;

    $equipeGponToday = Mesure::where('mesure_type', 'gpon')
        ->where('equipe_id', $equipeId)
        ->whereDate('mesure_date', $today)
        ->selectRaw('COUNT(DISTINCT num_ligne) as total')
        ->value('total') ?? 0;
}

// Récupérer le nom de l'équipe
$equipeName = $equipeId ? Equipe::find($equipeId)?->nom : 'Non assigné';
?>

<div id="dashboard-agent-container" class="dashboard-agent-wrapper">
<link rel="stylesheet" href="<?php echo e(asset('css/dashboard-cards.css')); ?>">

<style>
    /* Styles globaux pour la page */
    body {
        background-color: #f3f4f6;
    }

    /* Animation de pulsation pour les icônes */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Animation de brillance pour les cartes */
    @keyframes shine {
        0% {
            background-position: -100% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    /* Effet de brillance sur les cartes */
    .dashboard-stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        background-size: 200% 100%;
        animation: shine 3s infinite;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1;
        pointer-events: none;
    }

    .dashboard-stat-card:hover::after {
        opacity: 1;
    }

    /* Animation pour les icônes */
    .dashboard-stat-icon svg {
        animation: pulse 2s infinite ease-in-out;
    }

    /* Amélioration de l'espacement global */
    .dashboard-stats-container {
        padding: 0.5rem;
        margin-bottom: 2rem;
    }

    /* Animation pour le tableau */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .agent-info-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        padding: 1.5rem;
        margin-bottom: 2rem;
        animation: fadeIn 0.5s ease-out forwards;
        position: relative;
        overflow: hidden;
    }

    .agent-info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(45deg, #001e8c 0, #c832ff);
    }

    .agent-info-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .agent-info-content {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .agent-info-item {
        flex: 1;
        min-width: 200px;
    }

    .agent-info-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #4b5563;
    }

    .agent-info-value {
        font-size: 1rem;
        color: #1a202c;
    }
</style>

<div class="agent-info-card">
    <div class="agent-info-title">Informations de l'agent</div>
    <div class="agent-info-content">
        <div class="agent-info-item">
            <div class="agent-info-label">Nom</div>
            <div class="agent-info-value"><?php echo e($user->name); ?></div>
        </div>
        <div class="agent-info-item">
            <div class="agent-info-label">Équipe</div>
            <div class="agent-info-value"><?php echo e($equipeName); ?></div>
        </div>
        <div class="agent-info-item">
            <div class="agent-info-label">Date</div>
            <div class="agent-info-value"><?php echo e($today->format('d/m/Y')); ?></div>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="filter-container" style="background-color: white; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); padding: 2rem; margin-bottom: 2rem; position: relative; overflow: hidden;">
    <div style="content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 5px; background: linear-gradient(45deg, #001e8c 0, #c832ff);"></div>
    <form action="" method="GET" style="display: flex; flex-wrap: wrap; gap: 1.5rem; align-items: flex-end;">
        <div style="flex: 1; min-width: 200px; position: relative;">
            <label for="date" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #374151; font-size: 0.875rem;">Date</label>
            <input type="date" name="date" id="date" style="width: 100%; padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 0.5rem; font-size: 0.875rem; transition: all 0.3s ease; background-color: white;" value="<?php echo e($dateParam ?? $today->format('Y-m-d')); ?>">
        </div>
        <button type="submit" style="background: linear-gradient(45deg, #001e8c 0, #c832ff); color: white; border: none; padding: 0.75rem 2rem; border-radius: 0.5rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.875rem;">Filtrer</button>
    </form>
</div>

<div class="dashboard-stats-container">
    <!-- xDSL Cards - Agent aujourd'hui -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Aujourd'hui</div>
            <div class="dashboard-stat-value"><?php echo e($agentXdslToday); ?></div>
            <div class="dashboard-stat-type xdsl">Mes lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </div>
    </div>

    <!-- xDSL Cards - Agent moyenne -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Moyen Journalier</div>
            <div class="dashboard-stat-value"><?php echo e(round($agentXdslAvg, 2)); ?></div>
            <div class="dashboard-stat-type xdsl">Mes lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
    </div>

    <!-- xDSL Cards - Équipe aujourd'hui -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Équipe Aujourd'hui</div>
            <div class="dashboard-stat-value"><?php echo e($equipeXdslToday); ?></div>
            <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
        </div>
    </div>

    <!-- GPON Cards - Agent aujourd'hui -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Aujourd'hui</div>
            <div class="dashboard-stat-value"><?php echo e($agentGponToday); ?></div>
            <div class="dashboard-stat-type gpon">Mes lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </div>
    </div>

    <!-- GPON Cards - Agent moyenne -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Moyen Journalier</div>
            <div class="dashboard-stat-value"><?php echo e(round($agentGponAvg, 2)); ?></div>
            <div class="dashboard-stat-type gpon">Mes lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
    </div>

    <!-- GPON Cards - Équipe aujourd'hui -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Équipe Aujourd'hui</div>
            <div class="dashboard-stat-value"><?php echo e($equipeGponToday); ?></div>
            <div class="dashboard-stat-type gpon">Lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
        </div>
    </div>
</div>
</div><!-- End of dashboard-agent-wrapper -->
<?php /**PATH D:\Projects\nourtel\resources\views/partials/dashboardagent.blade.php ENDPATH**/ ?>