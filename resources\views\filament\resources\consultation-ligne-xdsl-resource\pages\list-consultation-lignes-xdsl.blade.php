<x-filament-panels::page
    @class([
        'fi-resource-list-records-page',
        'fi-resource-' . str_replace('/', '-', $this->getResource()::getSlug()),
    ])
>
    <div class="flex flex-col gap-y-6">
        <x-filament-panels::resources.tabs />

        {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE, scopes: $this->getRenderHookScopes()) }}

        @if($this->hasSearched)
            {{ $this->table }}
        @else
            <div class="flex flex-col items-center justify-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="text-center">
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">Aucune donnée à afficher</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">V<PERSON><PERSON><PERSON> effectuer une recherche par le numéro de ligne.</p>
                </div>
            </div>
        @endif

        {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_AFTER, scopes: $this->getRenderHookScopes()) }}
    </div>
</x-filament-panels::page>
