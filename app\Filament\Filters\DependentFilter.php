<?php

namespace App\Filament\Filters;

use App\Models\Equipe;
use App\Models\Region;
use App\Models\User;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class DependentFilter extends Filter
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->form([
            Grid::make(3)
                ->schema([
                    Select::make('region_id')
                        ->label('Région')
                        ->options(Region::pluck('nom', 'id'))
                        ->searchable()
                        ->preload()
                        ->live()
                        ->afterStateUpdated(fn (callable $set) => $set('equipe_id', null)),

                    Select::make('equipe_id')
                        ->label('Équipe')
                        ->options(function (callable $get) {
                            $regionId = $get('region_id');
                            
                            if (!$regionId) {
                                return Equipe::pluck('nom', 'id');
                            }
                            
                            return Region::find($regionId)
                                ?->equipes()
                                ->pluck('nom', 'id') ?? [];
                        })
                        ->searchable()
                        ->preload()
                        ->live()
                        ->afterStateUpdated(fn (callable $set) => $set('created_by', null)),

                    Select::make('created_by')
                        ->label('Agent')
                        ->options(function (callable $get) {
                            $equipeId = $get('equipe_id');
                            
                            if (!$equipeId) {
                                return User::pluck('name', 'id');
                            }
                            
                            return Equipe::find($equipeId)
                                ?->agents()
                                ->pluck('name', 'id') ?? [];
                        })
                        ->searchable()
                        ->preload(),
                ]),
                
            Grid::make(2)
                ->schema([
                    DatePicker::make('created_from')
                        ->label('Du'),
                    DatePicker::make('created_until')
                        ->label('Au'),
                ]),
                
            TextInput::make('num_ligne')
                ->label('Num Ligne')
                ->placeholder('Identifiant de la ligne'),
        ]);

        $this->query(function (Builder $query, array $data): Builder {
            return $query
                ->when(
                    $data['region_id'],
                    fn (Builder $query, $regionId): Builder => $query->where('region_id', $regionId),
                )
                ->when(
                    $data['equipe_id'],
                    fn (Builder $query, $equipeId): Builder => $query->where('equipe_id', $equipeId),
                )
                ->when(
                    $data['created_by'],
                    fn (Builder $query, $userId): Builder => $query->where('created_by', $userId),
                )
                ->when(
                    $data['created_from'],
                    fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                )
                ->when(
                    $data['created_until'],
                    fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                )
                ->when(
                    $data['num_ligne'],
                    fn (Builder $query, $numLigne): Builder => $query->where('num_ligne', 'like', "%{$numLigne}%"),
                );
        });
    }
}
