<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GPONServiceResource\Pages;
use App\Models\Mesure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Columns\TextColumn;

class GPONServiceResource extends Resource
{
    protected static ?string $model = Mesure::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Historique Qualification';
    protected static ?string $navigationGroup = 'Service GPON';
    protected static ?int $navigationSort = 2;
    protected static ?int $navigationGroupSort = 1; // Pour s'assurer que le groupe apparaît avant Administration

    public static function canAccess(): bool
    {
        // Autoriser l'accès à tous les utilisateurs authentifiés
        return Auth::check();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('created_by')
                    ->relationship('creator', 'name')
                    ->required()
                    ->disabled()
                    ->default(Auth::id()),

                Forms\Components\Select::make('equipe_id')
                    ->relationship('equipe', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->equipe_id),

                Forms\Components\Select::make('region_id')
                    ->relationship('region', 'nom')
                    ->required()
                    ->disabled()
                    ->default(Auth::user()->region_id),

                Forms\Components\Section::make('GPON Measurements')
                    ->schema([
                        Forms\Components\TextInput::make('g_rx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_tx_power')
                            ->numeric()
                            ->suffix('dBm'),
                        Forms\Components\TextInput::make('g_voltage')
                            ->numeric()
                            ->suffix('V'),
                        Forms\Components\TextInput::make('g_bias_current')
                            ->numeric()
                            ->suffix('mA'),
                        Forms\Components\TextInput::make('g_pon_alarm_info')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Etat')
                    ->schema([
                        Forms\Components\Select::make('mesure_type')
                            ->options([
                                'gpon' => 'GPON',
                                'xdsl' => 'xDSL',
                                'both' => 'Both',
                            ])
                            ->required()
                            ->default('gpon')
                            ->disabled(),
                        Forms\Components\Select::make('mesure_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                            ])
                            ->required(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = Auth::user();

        // Récupérer le rôle de l'utilisateur
        $userRole = DB::table('roles')
            ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('model_has_roles.model_type', get_class($user))
            ->select('roles.id')
            ->first();

        $query = Mesure::query()->where('mesure_type', 'gpon');

        // Si l'utilisateur est super_admin (id=1) ou admin (id=2), afficher toutes les mesures
        if ($userRole && ($userRole->id == 1 || $userRole->id == 2)) {
            // Ne pas ajouter de condition supplémentaire
        }
        // Si l'utilisateur est chef d'équipe (id=3), filtrer par ses équipes
        elseif ($userRole && $userRole->id == 3) {
            // Récupérer les équipes dont l'utilisateur est membre
            $equipeIds = DB::table('equipe_agent')
                ->where('user_id', $user->id)
                ->pluck('equipe_id')
                ->toArray();

            if (!empty($equipeIds)) {
                $query->whereIn('equipe_id', $equipeIds);
            } else {
                // Si l'utilisateur n'a pas d'équipes, ne rien afficher
                $query->whereRaw('1 = 0');
            }
        }

        // Pour les autres rôles, ne rien afficher
        else {
            $query->where('created_by', $user->id);
        }

        return $table
            ->query($query)
            ->searchable(false)
            ->filtersTriggerAction(null)
            ->deferFilters() // Prevent auto-refresh when filters change
            ->columns([
                TextColumn::make('num_ligne')
                    ->label('Num Ligne')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(fn ($record) => $record->num_ligne),
                TextColumn::make('emplacement')
                    ->label('Emplacement')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(fn ($record) => $record->emplacement),
                TextColumn::make('g_rx_power')
                    ->label('Puissance Rx')
                    ->numeric(
                        decimalPlaces: 2,
                        decimalSeparator: '.',
                        thousandsSeparator: ',',
                    )
                    ->suffix(' dBm')
                    ->sortable(),
                TextColumn::make('mesure_status')
                    ->label('Etat')
                    ->formatStateUsing(function (string $state): string {
                        $color = $state === '1' ? '#16a34a' : '#dc2626';
                        return "<div style='width: 20px; height: 20px; border-radius: 50%; background-color: {$color}; margin: 0 auto;'></div>";
                    })
                    ->html(),
                TextColumn::make('creator.name')
                    ->label('Créé Par')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('equipe.nom')
                    ->label('Équipe')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('region.nom')
                    ->label('Région')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('mesure_date')
                    ->label('Date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\ViewColumn::make('Localisation')
                    ->view('filament.resources.gpon-service-resource.pages.location-column')
                    ->alignCenter(),
            ])
            ->defaultSort('mesure_date', 'desc')
            ->paginated()
            ->recordUrl(null)
            ->actions([])
            ->filters([
                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Du'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('mesure_date', '>=', $date),
                            )
                            ->when(
                                $data['created_until'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('mesure_date', '<=', $date),
                            );
                    }),

                Tables\Filters\SelectFilter::make('region_filter')
                    ->form([
                        Forms\Components\Select::make('region_id')
                            ->label('Région')
                            ->options(\App\Models\Region::pluck('nom', 'id'))
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['region_id'] ?? null,
                            fn (Builder $query, $regionId): Builder => $query->where('region_id', $regionId),
                        );
                    }),

                Tables\Filters\SelectFilter::make('equipe_filter')
                    ->form([
                        Forms\Components\Select::make('equipe_id')
                            ->label('Équipe')
                            ->options(\App\Models\Equipe::pluck('nom', 'id'))
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['equipe_id'] ?? null,
                            fn (Builder $query, $equipeId): Builder => $query->where('equipe_id', $equipeId),
                        );
                    }),

                Tables\Filters\SelectFilter::make('agent_filter')
                    ->form([
                        Forms\Components\Select::make('created_by')
                            ->label('Agent')
                            ->options(\App\Models\User::pluck('name', 'id'))
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['created_by'] ?? null,
                            fn (Builder $query, $userId): Builder => $query->where('created_by', $userId),
                        );
                    }),

                Tables\Filters\Filter::make('num_ligne_filter')
                    ->form([
                        Forms\Components\TextInput::make('num_ligne')
                            ->label('Num Ligne')
                            ->placeholder('Recherche exacte par numéro'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['num_ligne'] ?? null,
                            fn (Builder $query, $numLigne): Builder => $query->where('num_ligne', '=', $numLigne),
                        );
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGPONServices::route('/'),
            'create' => Pages\CreateGPONService::route('/create'),
            'edit' => Pages\EditGPONService::route('/{record}/edit'),
        ];
    }


}