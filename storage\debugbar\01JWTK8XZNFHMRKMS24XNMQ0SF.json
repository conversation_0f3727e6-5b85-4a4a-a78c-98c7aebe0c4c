{"__meta": {"id": "01JWTK8XZNFHMRKMS24XNMQ0SF", "datetime": "2025-06-03 09:52:00", "utime": **********.502281, "method": "GET", "uri": "/api/users/27/locations?equipe_id=9", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748944319.767358, "end": **********.5023, "duration": 0.7349419593811035, "duration_str": "735ms", "measures": [{"label": "Booting", "start": 1748944319.767358, "relative_start": 0, "end": **********.289702, "relative_end": **********.289702, "duration": 0.****************, "duration_str": "522ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.289714, "relative_start": 0.****************, "end": **********.502302, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.461919, "relative_start": 0.****************, "end": **********.466907, "relative_end": **********.466907, "duration": 0.004987955093383789, "duration_str": "4.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.498324, "relative_start": 0.****************, "end": **********.498742, "relative_end": **********.498742, "duration": 0.0004181861877441406, "duration_str": "418μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.498772, "relative_start": 0.****************, "end": **********.498793, "relative_end": **********.498793, "duration": 2.09808349609375e-05, "duration_str": "21μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01789, "accumulated_duration_str": "17.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select [id], [num_ligne], [emplacement], [lat], [lng], [mesure_status], [created_at] from [mesures] where [created_by] = '27' and ([lat] != '0' or [lng] != '0') and [equipe_id] = '9' order by [id] asc", "type": "query", "params": [], "bindings": ["27", "0", "0", "9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\nourtel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4698381, "duration": 0.01789, "duration_str": "17.89ms", "memory": 0, "memory_str": null, "filename": "UserLocationController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UserLocationController.php", "file": "D:\\Projects\\nourtel\\app\\Http\\Controllers\\Api\\UserLocationController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=37", "ajax": false, "filename": "UserLocationController.php", "line": "37"}, "connection": "nourtel", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Mesure": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FModels%2FMesure.php&line=1", "ajax": false, "filename": "Mesure.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/27/locations?equipe_id=9", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations", "uri": "GET api/users/{userId}/locations", "controller": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fapp%2FHttp%2FControllers%2FApi%2FUserLocationController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/UserLocationController.php:18-40</a>", "middleware": "api", "duration": "735ms", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-513056063 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>equipe_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-513056063\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1493233777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1493233777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-91995582 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1261 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IngxWVZweWxLb1JPbkgrbnY5ckE2R1E9PSIsInZhbHVlIjoiUzlTc2RaUnAyRTE3eEZidUFHTDFpT0x1N2JTblJSREQzQVRYcGUrdXpKaE5ONFQzQk5ERlB1SjlBNk1Gck5panV6KzFYcElMVThpdGhDbm1yK0s3bkJqWEpxMHZzdUpFZkVFK05aNG8yOFN1VTc5dmJuNUsxclgxZTJRY2RWUUEiLCJtYWMiOiIwNGY0ZDE0NWRkMjFmZDk3NTZlYmQwOTc3OTExNmIxNzAzMTYzNjBjNDU3NGQ1Y2JhOWE3ZGU1YWY1NjYzN2M2IiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6ImRBd0ZjeWZGS1FVZ2JVeU16VUthQWc9PSIsInZhbHVlIjoiZEtXRlJqWXNQaCt4RitPV1JsOGV6Uks4S2I0Mmw3M1AyV1FkYXJSWHlwZlhZV0hoZUx2WEZGODIvZXNJMlZzZ0pqL1hrK1dTYzVycS9pU3VOTXhmNmdqbytKNkY0Vi8zdHZYdHR1ODd5Uk1IV1lObXhkenhJdVIzZStuOWNmck0iLCJtYWMiOiIxZWE2MDg0MzIzMjg2ZTI3NzI4MDYzMTI5YWI4ODQ1OWRlZGYyMmMxYTJjZDkxNmJmZWQ0MTYzZGY2ZDYxODhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6,de;q=0.5,la;q=0.4,gd;q=0.3,es;q=0.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://nourtel.test/?region_id=&amp;equipe_id=9&amp;date=2025-05-25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91995582\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-993235916 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IkpuKzJUL2g4bk1pLzR2bE5aYUMwa3c9PSIsInZhbHVlIjoiajViRWRMaEpSQ1lkbFQ3Z1VBeWFVdnRnZnF0cStmekU0S0gyTCtpL092bTlSeUFJVXhScDhjSk5OcHVBZ1RBcHNGQ2ltcGt5MHFzbERmT1BkTTlGN01lamt4U1RMRFY5amVETmpQMzc5R1paMUJxaGVQbklSODZOWC9YOWc3NmJEWThRNnZweVFiZk95ekdzK2tLMDhBNnFjaEFNUloxZzZxK0NhWHJjLzVkY0pNVXFidVY4VXlGKy9qQ2ZkT2RmUnRqSWRFZzFCWUpRdmVJSktXQVRpZlUrK2VPSk14dGlKSnIxRENleFJjST0iLCJtYWMiOiJhZGU2ODAzMGRkMTk2NTg5ZTgwMTBkNmJjY2NkYjdiMGQxYmVjZjgxNWM1N2JhN2ZiOGU3MTI2ZGM3MjY5OWU0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IngxWVZweWxLb1JPbkgrbnY5ckE2R1E9PSIsInZhbHVlIjoiUzlTc2RaUnAyRTE3eEZidUFHTDFpT0x1N2JTblJSREQzQVRYcGUrdXpKaE5ONFQzQk5ERlB1SjlBNk1Gck5panV6KzFYcElMVThpdGhDbm1yK0s3bkJqWEpxMHZzdUpFZkVFK05aNG8yOFN1VTc5dmJuNUsxclgxZTJRY2RWUUEiLCJtYWMiOiIwNGY0ZDE0NWRkMjFmZDk3NTZlYmQwOTc3OTExNmIxNzAzMTYzNjBjNDU3NGQ1Y2JhOWE3ZGU1YWY1NjYzN2M2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImRBd0ZjeWZGS1FVZ2JVeU16VUthQWc9PSIsInZhbHVlIjoiZEtXRlJqWXNQaCt4RitPV1JsOGV6Uks4S2I0Mmw3M1AyV1FkYXJSWHlwZlhZV0hoZUx2WEZGODIvZXNJMlZzZ0pqL1hrK1dTYzVycS9pU3VOTXhmNmdqbytKNkY0Vi8zdHZYdHR1ODd5Uk1IV1lObXhkenhJdVIzZStuOWNmck0iLCJtYWMiOiIxZWE2MDg0MzIzMjg2ZTI3NzI4MDYzMTI5YWI4ODQ1OWRlZGYyMmMxYTJjZDkxNmJmZWQ0MTYzZGY2ZDYxODhiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993235916\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-179132731 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 09:52:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179132731\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-328179005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-328179005\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/api/users/27/locations?equipe_id=9", "controller_action": "App\\Http\\Controllers\\Api\\UserLocationController@getUserLocations"}, "badge": null}}