<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Mesure;
use Illuminate\Http\Request;

class UserLocationController extends Controller
{
    /**
     * Récupérer les localisations d'un utilisateur avec filtres
     *
     * @param int $userId
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserLocations($userId, Request $request)
    {
        $query = Mesure::where('created_by', $userId)
            ->where(function($q) {
                $q->where('lat', '!=', '0')->orWhere('lng', '!=', '0');
            });

        // Appliquer le filtre de région si présent
        if ($request->has('region_id') && $request->region_id) {
            $query->where('region_id', $request->region_id);
        }

        // Appliquer le filtre d'équipe si présent
        if ($request->has('equipe_id') && $request->equipe_id) {
            $query->where('equipe_id', $request->equipe_id);
        }

        // Appliquer le filtre de date si présent
        if ($request->has('date') && $request->date) {
            $query->whereDate('mesure_date', $request->date);
        }

        $locations = $query->select('id', 'num_ligne', 'emplacement', 'lat', 'lng', 'mesure_status', 'mesure_date', 'created_at')
            ->orderBy('id', 'asc') // Tri par ID pour respecter l'ordre demandé
            ->get();

        return response()->json($locations);
    }
}
