<?php

namespace App\Providers;

use Filament\Support\Assets\Css;
use Filament\Support\Assets\Js;
use Filament\Support\Facades\FilamentAsset;
use Illuminate\Support\ServiceProvider;

class LocationMapsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Enregistrer les assets CSS et JavaScript pour les cartes de localisation
        FilamentAsset::register([
            // CSS pour Leaflet
            Css::make('leaflet-css', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'),
            // CSS personnalisé pour les modales de localisation
            Css::make('location-maps-css', asset('css/location-maps.css')),
            
            // JavaScript pour Leaflet
            Js::make('leaflet-js', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'),
            // JavaScript personnalisé pour les fonctions de carte
            Js::make('location-maps-js', asset('js/location-maps.js')),
        ]);
    }
}
