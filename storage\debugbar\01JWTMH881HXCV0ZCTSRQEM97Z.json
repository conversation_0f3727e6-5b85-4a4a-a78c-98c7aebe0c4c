{"__meta": {"id": "01JWTMH881HXCV0ZCTSRQEM97Z", "datetime": "2025-06-03 10:14:01", "utime": **********.730464, "method": "GET", "uri": "/livewire/livewire.js?id=fcf8c2ad", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748945640.946181, "end": **********.730479, "duration": 0.7842979431152344, "duration_str": "784ms", "measures": [{"label": "Booting", "start": 1748945640.946181, "relative_start": 0, "end": **********.407207, "relative_end": **********.407207, "duration": 0.****************, "duration_str": "461ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.407226, "relative_start": 0.****************, "end": **********.73048, "relative_end": 9.5367431640625e-07, "duration": 0.***************, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.595971, "relative_start": 0.****************, "end": **********.598564, "relative_end": **********.598564, "duration": 0.002592802047729492, "duration_str": "2.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.728448, "relative_start": 0.****************, "end": **********.728974, "relative_end": **********.728974, "duration": 0.0005261898040771484, "duration_str": "526μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.728992, "relative_start": 0.****************, "end": **********.729029, "relative_end": **********.729029, "duration": 3.695487976074219e-05, "duration_str": "37μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.2.0", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "nourtel.test", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/livewire.js?id=fcf8c2ad", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fnourtel%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "784ms", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-145876575 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">fcf8c2ad</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145876575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-411627105 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-411627105\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-860341607 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"719 characters\">XSRF-TOKEN=eyJpdiI6IlJSbWl5RlRsbjMvaVUxWWhUcStVWUE9PSIsInZhbHVlIjoiMlBIdTROV21CSDZyWGpuWUNzYndNbUlZYkh1dFVlVEI5TEtydFpyck8zYStKc2E3c0ZhNWZBTytvbkIweEdJRTBoVCsyN1hYR083WnRCTDg3b3dDYXNLS0JXZmYxa0pTTklBcGJKNlFOMUxCVDNHRVg2UXpIb0xDY2YycUhMM00iLCJtYWMiOiI0OTc5NzhlZmRiODJhYmNjOWYxNWFlOGZhOTc1ZmNmMjNiNmM1ZWVmMzllZGU3NzY5MmQ4YTk2Nzc3YzY3MGJkIiwidGFnIjoiIn0%3D; line_analyzer_session=eyJpdiI6InVxUUFuRkl4eFBEaUVhRllQUlJEZnc9PSIsInZhbHVlIjoiYW4wK1VGYjg1NC9PZ1ZCVXVWRjIxczlUZ1luLzdVNWlqMytBOUlCL1BGTkFDYmkzYkxzM2hkbW5xV1pqZmI2cklvVGFIMVpEaVlEV3R2MkJzbnB6bWN5dFJ6WWJSRHZlZi9ZL1dTV1ZYSWZsRGxLSTRJTEl6bWxIYTRXajl4ZTYiLCJtYWMiOiJlNGRlOTA1ZWZiYTFkMTk3OWQwYjI5ZTk2MmVlYTBiZDMzOWE4YWVjNTM2YjY5MjE4Y2E1NGRmMDRiODBhMjdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://nourtel.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">nourtel.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860341607\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1705366910 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlJSbWl5RlRsbjMvaVUxWWhUcStVWUE9PSIsInZhbHVlIjoiMlBIdTROV21CSDZyWGpuWUNzYndNbUlZYkh1dFVlVEI5TEtydFpyck8zYStKc2E3c0ZhNWZBTytvbkIweEdJRTBoVCsyN1hYR083WnRCTDg3b3dDYXNLS0JXZmYxa0pTTklBcGJKNlFOMUxCVDNHRVg2UXpIb0xDY2YycUhMM00iLCJtYWMiOiI0OTc5NzhlZmRiODJhYmNjOWYxNWFlOGZhOTc1ZmNmMjNiNmM1ZWVmMzllZGU3NzY5MmQ4YTk2Nzc3YzY3MGJkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>line_analyzer_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InVxUUFuRkl4eFBEaUVhRllQUlJEZnc9PSIsInZhbHVlIjoiYW4wK1VGYjg1NC9PZ1ZCVXVWRjIxczlUZ1luLzdVNWlqMytBOUlCL1BGTkFDYmkzYkxzM2hkbW5xV1pqZmI2cklvVGFIMVpEaVlEV3R2MkJzbnB6bWN5dFJ6WWJSRHZlZi9ZL1dTV1ZYSWZsRGxLSTRJTEl6bWxIYTRXajl4ZTYiLCJtYWMiOiJlNGRlOTA1ZWZiYTFkMTk3OWQwYjI5ZTk2MmVlYTBiZDMzOWE4YWVjNTM2YjY5MjE4Y2E1NGRmMDRiODBhMjdhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705366910\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-907509269 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 03 Jun 2026 10:14:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 12 Mar 2025 20:24:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:14:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347449</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907509269\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1070410613 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070410613\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://nourtel.test/livewire/livewire.js?id=fcf8c2ad", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}