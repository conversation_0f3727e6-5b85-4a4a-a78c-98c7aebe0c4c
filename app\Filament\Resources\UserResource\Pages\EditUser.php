<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\EditRecord;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Impersonate::make()->record($this->getRecord())
            // <--
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        $user = $this->record;
        $data = $this->data;

        // Récupérer les équipes sélectionnées et le rôle utilisateur
        $equipes = $data['equipes'] ?? [];
        $roleId = $data['roles'] ?? null;

        // Déterminer le type de relation en fonction du rôle de l'utilisateur
        $isChefEquipe = ($roleId == 3); // ID 3 = chef d'équipe
        $isAgent = ($roleId == 4); // ID 4 = agent

        // Si l'utilisateur est un chef d'équipe, l'ajouter comme responsable
        if ($isChefEquipe) {
            $user->responsables()->sync($equipes);
            $user->agents()->detach(); // S'assurer qu'il n'est pas aussi membre
        }
        // Si l'utilisateur est un agent, l'ajouter comme membre
        elseif ($isAgent) {
            $user->agents()->sync($equipes);
            $user->responsables()->detach(); // S'assurer qu'il n'est pas aussi responsable
        }
    }
}
