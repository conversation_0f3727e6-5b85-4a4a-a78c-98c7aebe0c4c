@php
use App\Models\Mesure;
use App\Models\Region;
use App\Models\Equipe;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

$dateParam = request('date');
$today = $dateParam ? Carbon::parse($dateParam) : Carbon::today();
$selectedRegion = request('region_id');
$selectedEquipe = request('equipe_id');

// Récupérer toutes les régions pour le filtre
$regions = Region::orderBy('nom')->get();

// Récupérer les équipes en fonction de la région sélectionnée
if ($selectedRegion) {
    $equipes = Equipe::whereHas('regions', function($query) use ($selectedRegion) {
        $query->where('regions.id', $selectedRegion);
    })->orderBy('nom')->get();
} else {
    $equipes = Equipe::orderBy('nom')->get();
}

// Construire la requête de base
$xdslQuery = Mesure::where('mesure_type', 'xdsl');
$gponQuery = Mesure::where('mesure_type', 'gpon');

// Appliquer les filtres si sélectionnés
if ($selectedRegion) {
    $xdslQuery->where('region_id', $selectedRegion);
    $gponQuery->where('region_id', $selectedRegion);
}

if ($selectedEquipe) {
    $xdslQuery->where('equipe_id', $selectedEquipe);
    $gponQuery->where('equipe_id', $selectedEquipe);
}

// xDSL
$xdslToday = (clone $xdslQuery)->whereDate('mesure_date', $today)
    ->selectRaw('COUNT(DISTINCT num_ligne) as total')
    ->value('total') ?? 0;
$xdslAvg = (clone $xdslQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->get()
    ->avg('total') ?? 0;
$xdslMax = (clone $xdslQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->orderByDesc('total')
    ->first()?->total ?? 0;

// GPON
$gponToday = (clone $gponQuery)->whereDate('mesure_date', $today)
    ->selectRaw('COUNT(DISTINCT num_ligne) as total')
    ->value('total') ?? 0;
$gponAvg = (clone $gponQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->get()
    ->avg('total') ?? 0;
$gponMax = (clone $gponQuery)->whereMonth('mesure_date', $today->month)
    ->selectRaw('CONVERT(DATE, mesure_date) as date_only, COUNT(DISTINCT num_ligne) as total')
    ->groupByRaw('CONVERT(DATE, mesure_date)')
    ->orderByDesc('total')
    ->first()?->total ?? 0;

// Récupérer les performances des agents pour la date sélectionnée
$baseQuery = Mesure::select('created_by')
    ->whereNotNull('created_by')
    ->whereDate('mesure_date', $today);

// Appliquer les filtres si sélectionnés
if ($selectedRegion) {
    $baseQuery->where('region_id', $selectedRegion);
}

if ($selectedEquipe) {
    $baseQuery->where('equipe_id', $selectedEquipe);
}

// Récupérer les statistiques par utilisateur pour la date sélectionnée (distinct num_ligne)
$userStats = $baseQuery->groupBy('created_by')
    ->selectRaw('created_by,
                COUNT(DISTINCT CASE WHEN mesure_type = \'xdsl\' THEN num_ligne END) as xdsl_count,
                COUNT(DISTINCT CASE WHEN mesure_type = \'gpon\' THEN num_ligne END) as gpon_count')
    ->havingRaw('COUNT(DISTINCT CASE WHEN mesure_type = \'xdsl\' THEN num_ligne END) + COUNT(DISTINCT CASE WHEN mesure_type = \'gpon\' THEN num_ligne END) > 0')
    ->get();

// Récupérer les informations des utilisateurs
$userIds = $userStats->pluck('created_by')->toArray();
$users = User::whereIn('id', $userIds)->get()->keyBy('id');

// Combiner les données
$agentPerformance = $userStats->map(function($stat) use ($users, $today) {
    $user = $users[$stat->created_by] ?? null;
    return [
        'created_by' => $stat->created_by,
        'name' => $user ? $user->name : 'Utilisateur inconnu',
        'xdsl_count' => $stat->xdsl_count,
        'gpon_count' => $stat->gpon_count,
        'has_locations' => Mesure::where('created_by', $stat->created_by)
            ->whereDate('mesure_date', $today)
            ->whereNotNull('lat')
            ->whereNotNull('lng')
            ->exists()
    ];
})->sortByDesc(function($stat) {
    return $stat['xdsl_count'] + $stat['gpon_count'];
});
@endphp

<div id="dashboard-admin-container">
<link rel="stylesheet" href="{{ asset('css/dashboard-cards.css') }}">

<style>
    /* Styles globaux pour la page */
    body {
        background-color: #f3f4f6;
    }

    /* Animation de pulsation pour les icônes */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Animation de brillance pour les cartes */
    @keyframes shine {
        0% {
            background-position: -100% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    /* Effet de brillance sur les cartes */
    .dashboard-stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        background-size: 200% 100%;
        animation: shine 3s infinite;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1;
        pointer-events: none;
    }

    .dashboard-stat-card:hover::after {
        opacity: 1;
    }

    /* Animation pour les icônes */
    .dashboard-stat-icon svg {
        animation: pulse 2s infinite ease-in-out;
    }

    /* Style pour le bouton de filtre lors de la soumission */
    .filter-button.submitting {
        position: relative;
        overflow: hidden;
    }

    .filter-button.submitting::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        animation: shine 1.5s infinite;
    }

    /* Amélioration de l'espacement global */
    .dashboard-stats-container {
        padding: 0.5rem;
        margin-bottom: 2rem;
    }

    /* Effet de survol amélioré pour les sélecteurs */
    .filter-select:hover {
        box-shadow: 0 0 0 3px rgba(200, 50, 255, 0.1);
    }

    /* Styles pour le tableau de performance des agents */
    .performance-table-container {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-top: 2rem;
    }

    .performance-table {
        width: 100%;
        border-collapse: collapse;
    }

    .performance-table th {
        background: linear-gradient(45deg, #4299e1, #63b3ed);
        color: white;
        font-weight: 600;
        text-align: center;
        padding: 1rem;
        position: relative;
    }

    .performance-table th:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 25%;
        height: 50%;
        width: 1px;
        background-color: rgba(255, 255, 255, 0.3);
    }

    .performance-table td {
        padding: 1rem;
        text-align: center;
        border-bottom: 1px solid #e2e8f0;
        background-color: #edf2f7;
    }

    .performance-table tr:nth-child(even) td {
        background-color: #f8fafc;
    }

    .performance-table tr:hover td {
        background-color: #ebf4ff;
    }

    .location-button {
        background: linear-gradient(45deg, #3182ce, #4299e1);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .leaflet-popup-content {
        margin: 3px 20px!important;
    }

    .location-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .location-button:active {
        transform: translateY(0);
    }

    .location-button svg {
        width: 1.25rem;
        height: 1.25rem;
    }

    /* Styles pour le modal */
    .location-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }

    .location-modal.active {
        display: flex;
    }

    .modal-content {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        width: 80%;
        max-width: 1000px;
        max-height: 80vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #718096;
        transition: color 0.3s;
    }

    .modal-close:hover {
        color: #e53e3e;
    }

    .modal-body {
        flex: 1;
        overflow: hidden;
    }

    #map-container {
        width: 100%;
        height: 500px;
    }

    /* Animation pour le tableau */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .performance-table-container {
        animation: fadeIn 0.5s ease-out forwards;
    }
</style>

<style>
    :root {
        --primary-gradient: linear-gradient(45deg, #001e8c 0, #c832ff);
        --filter-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --filter-border-radius: 1rem;
        --transition-speed: 0.3s;
    }

    .filter-container {
        background-color: white;
        border-radius: var(--filter-border-radius);
        box-shadow: var(--filter-shadow);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .filter-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: var(--primary-gradient);
    }

    .filter-form {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        align-items: flex-end;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
        position: relative;
    }

    .filter-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: #4b5563;
        transition: color var(--transition-speed);
    }

    .filter-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        border: 2px solid #e5e7eb;
        background-color: #f9fafb;
        font-size: 1rem;
        transition: all var(--transition-speed);
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 1rem;
        padding-right: 2.5rem;
    }

    .filter-select:focus {
        outline: none;
        border-color: #c832ff;
        box-shadow: 0 0 0 3px rgba(200, 50, 255, 0.2);
    }

    .filter-select:hover {
        border-color: #c832ff;
    }

    .filter-button {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all var(--transition-speed);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .filter-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
    }

    .filter-button:active {
        transform: translateY(0);
    }

    .filter-button::after {
        content: '';
        display: inline-block;
        width: 1.25rem;
        height: 1.25rem;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z'%3E%3C/path%3E%3C/svg%3E");
        background-size: contain;
        margin-left: 0.5rem;
    }

    @media (max-width: 768px) {
        .filter-form {
            flex-direction: column;
        }

        .filter-button {
            width: 100%;
        }
    }
</style>

<div class="filter-container">
    <form action="" method="GET" class="filter-form" id="dashboard-filter-form">
        <div class="filter-group">
            <label for="region_id" class="filter-label">Région</label>
            <select name="region_id" id="region_id" class="filter-select">
                <option value="">Toutes les régions</option>
                @foreach($regions as $region)
                    <option value="{{ $region->id }}" {{ $selectedRegion == $region->id ? 'selected' : '' }}>
                        {{ $region->nom }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="filter-group">
            <label for="equipe_id" class="filter-label">Équipe</label>
            <select name="equipe_id" id="equipe_id" class="filter-select">
                <option value="">Toutes les équipes</option>
                @foreach($equipes as $equipe)
                    <option value="{{ $equipe->id }}" {{ $selectedEquipe == $equipe->id ? 'selected' : '' }}>
                        {{ $equipe->nom }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="filter-group">
            <label for="date" class="filter-label">Date</label>
            <input type="date" name="date" id="date" class="filter-select" value="{{ $dateParam ?? $today->format('Y-m-d') }}">
        </div>

        <button type="submit" class="filter-button">Filtrer</button>
    </form>
</div>

<script>

        // Fonction pour afficher un indicateur de chargement
        function showLoading(element) {
            element.classList.add('loading');
            element.style.opacity = '0.7';
            element.style.pointerEvents = 'none';
        }

        // Fonction pour masquer l'indicateur de chargement
        function hideLoading(element) {
            element.classList.remove('loading');
            element.style.opacity = '1';
            element.style.pointerEvents = 'auto';
        }

        // Fonction pour animer l'apparition des options
        function animateOptions(select) {
            const options = select.querySelectorAll('option');
            options.forEach((option, index) => {
                option.style.opacity = '0';
                option.style.transform = 'translateY(10px)';
                setTimeout(() => {
                    option.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    option.style.opacity = '1';
                    option.style.transform = 'translateY(0)';
                }, index * 30);
            });
        }

        // Fonction pour mettre à jour les équipes en fonction de la région sélectionnée
        regionSelect.addEventListener('change', function() {
            const regionId = this.value;

            // Afficher l'indicateur de chargement
            showLoading(equipeSelect);

            // Réinitialiser le sélecteur d'équipe
            equipeSelect.innerHTML = '<option value="">Toutes les équipes</option>';

            const fetchUrl = regionId ? `/api/regions/${regionId}/equipes` : '/api/equipes';

            // Faire une requête AJAX pour obtenir les équipes
            fetch(fetchUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur réseau');
                    }
                    return response.json();
                })
                .then(data => {
                    // Ajouter les options avec un délai pour l'animation
                    data.forEach(equipe => {
                        const option = document.createElement('option');
                        option.value = equipe.id;
                        option.textContent = equipe.nom;
                        equipeSelect.appendChild(option);
                    });

                    // Masquer l'indicateur de chargement
                    hideLoading(equipeSelect);

                    // Animer l'apparition des options
                    animateOptions(equipeSelect);
                })
                .catch(error => {
                    console.error('Erreur lors de la récupération des équipes:', error);
                    hideLoading(equipeSelect);

                    // Afficher un message d'erreur
                    const errorOption = document.createElement('option');
                    errorOption.disabled = true;
                    errorOption.textContent = 'Erreur lors du chargement des équipes';
                    equipeSelect.appendChild(errorOption);
                });
        });

        // Ajouter une animation au bouton lors de la soumission
        filterForm.addEventListener('submit', function(e) {
            filterButton.innerHTML = 'Chargement...';
            filterButton.style.width = filterButton.offsetWidth + 'px';
            filterButton.classList.add('submitting');

            // Ajouter une classe pour l'animation de chargement
            document.querySelectorAll('.dashboard-stat-card').forEach(card => {
                card.style.opacity = '0.7';
                card.style.transform = 'scale(0.98)';
            });
        });

        // Animer les cartes de statistiques au chargement
        const statCards = document.querySelectorAll('.dashboard-stat-card');
        statCards.forEach((card, index) => {
            // Initialiser les styles
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            // Animation d'entrée avec délai progressif
            setTimeout(() => {
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';

                // Animer les éléments internes
                const content = card.querySelector('.dashboard-stat-content');
                const icon = card.querySelector('.dashboard-stat-icon');

                if (content) {
                    content.style.opacity = '0';
                    content.style.transform = 'translateX(-10px)';
                    setTimeout(() => {
                        content.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        content.style.opacity = '1';
                        content.style.transform = 'translateX(0)';
                    }, 200);
                }

                if (icon) {
                    icon.style.opacity = '0';
                    icon.style.transform = 'translateX(10px)';
                    setTimeout(() => {
                        icon.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        icon.style.opacity = '1';
                        icon.style.transform = 'translateX(0)';
                    }, 300);
                }
            }, 100 + index * 100);
        });
    });
</script>

<div class="dashboard-stats-container">
    <!-- xDSL Cards -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Aujourd'hui</div>
            <div class="dashboard-stat-value">{{ $xdslToday }}</div>
            <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Moyen Journalier</div>
            <div class="dashboard-stat-value">{{ round($xdslAvg, 2) }}</div>
            <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Nombre Maximum</div>
            <div class="dashboard-stat-value">{{ $xdslMax }}</div>
            <div class="dashboard-stat-type xdsl">Lignes xDSL</div>
        </div>
        <div class="dashboard-stat-icon xdsl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
        </div>
    </div>

    <!-- GPON Cards -->
    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Aujourd'hui</div>
            <div class="dashboard-stat-value">{{ $gponToday }}</div>
            <div class="dashboard-stat-type gpon">Lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Moyen Journalier</div>
            <div class="dashboard-stat-value">{{ round($gponAvg, 2) }}</div>
            <div class="dashboard-stat-type gpon">Lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
    </div>

    <div class="dashboard-stat-card">
        <div class="dashboard-stat-content">
            <div class="dashboard-stat-label">Nombre Maximum</div>
            <div class="dashboard-stat-value">{{ $gponMax }}</div>
            <div class="dashboard-stat-type gpon">Lignes GPON</div>
        </div>
        <div class="dashboard-stat-icon gpon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
        </div>
    </div>
</div>

<!-- Tableau de performance des agents -->
<div class="performance-table-container">
    <table class="performance-table">
        <thead>
            <tr>
                <th>Utilisateur</th>
                <th>Nbre de lignes xDSL</th>
                <th>Nbre de lignes GPON</th>
                <th>Localisation</th>
            </tr>
        </thead>
        <tbody>
            @forelse($agentPerformance as $agent)
                <tr>
                    <td>{{ $agent['name'] }}</td>
                    <td>{{ $agent['xdsl_count'] }}</td>
                    <td>{{ $agent['gpon_count'] }}</td>
                    <td>
                        @if($agent['has_locations'])
                            <button type="button" class="location-button" onclick="openLocationModal({{ $agent['created_by'] }}, '{{ $agent['name'] }}')">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Voir localisation
                            </button>
                        @else
                            <span class="text-gray-500">Aucune localisation</span>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center py-4">Aucune donnée disponible</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Modal pour afficher les localisations -->
<div id="location-modal" class="location-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Localisations de <span id="modal-user-name"></span></h3>
            <button type="button" class="modal-close" onclick="closeLocationModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="map-container"></div>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
    let map;
    let markers = [];

    function openLocationModal(userId, userName) {
        // Afficher le modal
        document.getElementById('location-modal').classList.add('active');
        document.getElementById('modal-user-name').textContent = userName;

        // Initialiser la carte si elle n'existe pas encore
        if (!map) {
            map = L.map('map-container').setView([0, 0], 2);

            // Utiliser le tile server Google Maps comme spécifié dans les mémoires
            L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                attribution: '© Google Maps',
                maxZoom: 50
            }).addTo(map);
        }

        // Nettoyer les marqueurs existants
        markers.forEach(marker => map.removeLayer(marker));
        markers = [];

        // Récupérer les valeurs des filtres actuels
        const regionId = document.getElementById('region_id')?.value || '';
        const equipeId = document.getElementById('equipe_id')?.value || '';
        const selectedDate = document.getElementById('date')?.value || '';

        // Construire l'URL avec les paramètres de filtre
        const url = new URL(`/api/users/${userId}/locations`, window.location.origin);
        if (regionId) url.searchParams.append('region_id', regionId);
        if (equipeId) url.searchParams.append('equipe_id', equipeId);
        if (selectedDate) url.searchParams.append('date', selectedDate);

        // Récupérer les localisations de l'utilisateur avec les filtres
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.length === 0) {
                    // Aucune localisation trouvée
                    return;
                }

                const bounds = L.latLngBounds();

                // Ajouter les marqueurs pour chaque localisation
                const allMarkers = [];

                // Trier les données par ID
                data.sort((a, b) => a.id - b.id);

                data.forEach((location, index) => {
                    if (location.lat && location.lng) {
                        // Numéro d'ordre (1-based)
                        const orderNumber = index + 1;

                        // Formater la date et l'heure
                        let dateTime = 'N/A';
                        if (location.created_at) {
                            const date = new Date(location.created_at);
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const hours = String(date.getHours()).padStart(2, '0');
                            const minutes = String(date.getMinutes()).padStart(2, '0');

                            dateTime = `${year}-${month}-${day} / ${hours}:${minutes}`;
                        }

                        // Créer un popup permanent
                        const popup = L.popup({
                            autoClose: false,
                            closeOnClick: false
                        })
                        .setContent(`
                            <strong>${location.num_ligne || 'N/A'}</strong><strong>(${orderNumber})</strong><br>
                            ${location.emplacement || 'N/A'}<br>
                            <span style="color: ${location.mesure_status === '1' ? 'green' : 'red'}">●</span>
                            ${dateTime}
                        `);

                        // Créer une icône personnalisée avec le numéro d'ordre
                        const customIcon = L.divIcon({
                            className: 'custom-div-icon',
                            html: `<div style="background-color: #3182ce; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; justify-content: center; align-items: center; font-weight: bold; box-shadow: 0 2px 5px rgba(0,0,0,0.3);">${orderNumber}</div>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 12]
                        });

                        // Créer le marqueur avec l'icône personnalisée et le popup
                        const marker = L.marker([location.lat, location.lng], { icon: customIcon })
                            .addTo(map)
                            .bindPopup(popup);

                        allMarkers.push(marker);
                        markers.push(marker);
                        bounds.extend([location.lat, location.lng]);
                    }
                });

                // Ouvrir tous les popups après que tous les marqueurs ont été ajoutés
                allMarkers.forEach(marker => marker.openPopup());

                // Ajuster la vue de la carte pour montrer tous les marqueurs
                if (bounds.isValid()) {
                    map.fitBounds(bounds, { padding: [50, 50] });
                }
            })
            .catch(error => {
                console.error('Erreur lors de la récupération des localisations:', error);
            });

        // Redimensionner la carte après l'ouverture du modal
        setTimeout(() => {
            map.invalidateSize();
        }, 100);
    }

    function closeLocationModal() {
        document.getElementById('location-modal').classList.remove('active');
    }

    // Fermer le modal si l'utilisateur clique en dehors du contenu
    document.getElementById('location-modal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeLocationModal();
        }
    });
</script>