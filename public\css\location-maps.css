/* Styles for GPON Consultation Location Modal */
.consultation-location-modal {
    position: fixed;
    inset: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
}

.consultation-location-modal-content {
    width: 95vw;
    height: 80vh;
    max-width: 2400px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.consultation-location-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.consultation-location-modal-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: black !important;
}

.consultation-location-modal-body {
    padding: 0.75rem;
    flex: 1;
    overflow: hidden;
}

.consultation-location-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    height: 100%;
}

.consultation-location-map-container {
    height: 100%;
    min-height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.consultation-location-details-container {
    padding: 1rem;
}

.consultation-location-details {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;
}

.consultation-detail-row {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: black !important;
    border-bottom: 1px solid #e5e7eb;
}

.consultation-detail-row:last-child {
    border-bottom: none;
}

/* Styles for GPON Service Location Modal */
.location-modal {
    position: fixed;
    inset: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
}

.location-modal-content {
    width: 95vw;
    height: 80vh;
    max-width: 2400px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.location-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.location-modal-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: black !important;
}

.location-modal-body {
    padding: 0.75rem;
    flex: 1;
    overflow: hidden;
}

.location-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    height: 100%;
}

.location-map-container {
    height: 100%;
    min-height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.location-details-container {
    padding: 1rem;
}

.location-details {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;
}

.detail-row {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: black !important;
    border-bottom: 1px solid #e5e7eb;
}

.detail-row:last-child {
    border-bottom: none;
}

/* Styles for xDSL Consultation Location Modal */
.consultation-xdsl-location-modal {
    position: fixed;
    inset: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
}

.consultation-xdsl-location-modal-content {
    width: 95vw;
    height: 80vh;
    max-width: 2400px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.consultation-xdsl-location-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.consultation-xdsl-location-modal-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: black !important;
}

.consultation-xdsl-location-modal-body {
    padding: 0.75rem;
    flex: 1;
    overflow: hidden;
}

.consultation-xdsl-location-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    height: 100%;
}

.consultation-xdsl-location-map-container {
    height: 100%;
    min-height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.consultation-xdsl-location-details-container {
    padding: 1rem;
}

.consultation-xdsl-location-details {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;
}

.consultation-xdsl-detail-row {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: black !important;
    border-bottom: 1px solid #e5e7eb;
}

.consultation-xdsl-detail-row:last-child {
    border-bottom: none;
}

/* Styles for xDSL Service Location Modal */
.xdsl-location-modal {
    position: fixed;
    inset: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
}

.xdsl-location-modal-content {
    width: 95vw;
    height: 80vh;
    max-width: 2400px;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.xdsl-location-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.xdsl-location-modal-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: black !important;
}

.xdsl-location-modal-body {
    padding: 0.75rem;
    flex: 1;
    overflow: hidden;
}

.xdsl-location-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    height: 100%;
}

.xdsl-location-map-container {
    height: 100%;
    min-height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.xdsl-location-details-container {
    padding: 1rem;
}

.xdsl-location-details {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;
}

@media (max-width: 768px) {
    .consultation-location-info-grid,
    .consultation-xdsl-location-info-grid,
    .xdsl-location-info-grid,
    .location-info-grid {
        grid-template-columns: 1fr;
    }

    .consultation-location-map-container,
    .consultation-xdsl-location-map-container,
    .xdsl-location-map-container,
    .location-map-container {
        min-height: 300px;
    }
}
